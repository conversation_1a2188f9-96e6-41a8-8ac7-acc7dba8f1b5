"""
Language detection and context management middleware.

This middleware handles language detection from various sources and sets
the appropriate language context for database routing.
"""

import re
from django.conf import settings
from django.utils import translation
from django.utils.deprecation import MiddlewareMixin
from django.http import HttpResponseRedirect
from django.urls import reverse
from .routers import set_current_language, get_current_language


class LanguageDetectionMiddleware(MiddlewareMixin):
    """
    Middleware to detect user language preference and set database context.
    
    Language detection priority:
    1. URL parameter (?lang=tr)
    2. Language cookie
    3. Accept-Language header
    4. Default language from settings
    """
    
    def __init__(self, get_response):
        super().__init__(get_response)
        self.get_response = get_response
        
        # Compile regex for Accept-Language header parsing
        self.accept_language_re = re.compile(
            r'([a-z]{1,8}(?:-[a-z]{1,8})?)\s*(?:;\s*q\s*=\s*(1|0\.[0-9]+))?',
            re.IGNORECASE
        )
    
    def process_request(self, request):
        """Process the request to detect and set language."""
        
        # 1. Check URL parameter first (highest priority)
        language = request.GET.get('lang')
        if language and self._is_valid_language(language):
            self._set_language_context(request, language)
            return None
        
        # 2. Check language cookie
        language = request.COOKIES.get(settings.LANGUAGE_COOKIE_NAME)
        if language and self._is_valid_language(language):
            self._set_language_context(request, language)
            return None
        
        # 3. Parse Accept-Language header
        language = self._parse_accept_language(request)
        if language:
            self._set_language_context(request, language)
            return None
        
        # 4. Fall back to default language
        default_language = getattr(settings, 'DEFAULT_CONTENT_LANGUAGE', 'en')
        self._set_language_context(request, default_language)
        
        return None
    
    def process_response(self, request, response):
        """Process the response to set language cookie if needed."""
        
        # Get the current language from the request
        current_language = getattr(request, 'LANGUAGE_CODE', None)
        
        # Set language cookie if language was detected from URL parameter
        if (hasattr(request, '_language_from_url') and 
            request._language_from_url and 
            current_language):
            
            response.set_cookie(
                settings.LANGUAGE_COOKIE_NAME,
                current_language,
                max_age=settings.LANGUAGE_COOKIE_AGE,
                path=settings.LANGUAGE_COOKIE_PATH,
                domain=settings.LANGUAGE_COOKIE_DOMAIN,
                secure=request.is_secure(),
                httponly=True,
                samesite='Lax'
            )
        
        return response
    
    def _set_language_context(self, request, language):
        """Set language context for the request."""
        
        # Set Django's language context
        translation.activate(language)
        request.LANGUAGE_CODE = language
        
        # Set our custom language context for database routing
        set_current_language(language)
        
        # Mark if language came from URL parameter
        if request.GET.get('lang') == language:
            request._language_from_url = True
        
        # Store language in request for later use
        request.detected_language = language
    
    def _is_valid_language(self, language):
        """Check if the language is supported."""
        supported_languages = [lang[0] for lang in settings.LANGUAGES]
        return language in supported_languages
    
    def _parse_accept_language(self, request):
        """Parse Accept-Language header and return best matching language."""
        
        accept_language = request.META.get('HTTP_ACCEPT_LANGUAGE', '')
        if not accept_language:
            return None
        
        # Parse the Accept-Language header
        languages = []
        for match in self.accept_language_re.finditer(accept_language):
            lang = match.group(1).lower()
            quality = match.group(2)
            
            if quality:
                try:
                    quality = float(quality)
                except ValueError:
                    quality = 1.0
            else:
                quality = 1.0
            
            languages.append((lang, quality))
        
        # Sort by quality (highest first)
        languages.sort(key=lambda x: x[1], reverse=True)
        
        # Find the best matching supported language
        supported_languages = [lang[0] for lang in settings.LANGUAGES]
        
        for lang, quality in languages:
            # Check exact match first
            if lang in supported_languages:
                return lang
            
            # Check language prefix (e.g., 'en-US' -> 'en')
            lang_prefix = lang.split('-')[0]
            if lang_prefix in supported_languages:
                return lang_prefix
        
        return None


class DatabaseLanguageMiddleware(MiddlewareMixin):
    """
    Middleware to ensure database context is properly set for each request.
    
    This middleware works in conjunction with LanguageDetectionMiddleware
    to ensure the database routing context is maintained throughout the request.
    """
    
    def process_request(self, request):
        """Ensure database language context is set."""
        
        # Get language from request (set by LanguageDetectionMiddleware)
        language = getattr(request, 'LANGUAGE_CODE', None)
        
        if not language:
            # Fallback to default language if not set
            language = getattr(settings, 'DEFAULT_CONTENT_LANGUAGE', 'en')
            request.LANGUAGE_CODE = language
        
        # Ensure database routing context is set
        set_current_language(language)
        
        return None
    
    def process_response(self, request, response):
        """Clean up language context after request."""
        
        # The language context will be cleaned up automatically
        # when the thread ends, but we can be explicit about it
        return response


class LanguageRedirectMiddleware(MiddlewareMixin):
    """
    Optional middleware to redirect users to language-specific URLs.
    
    This can be used if you want to have language prefixes in URLs
    like /en/about/ or /tr/hakkimizda/
    """
    
    def process_request(self, request):
        """Check if request needs language redirect."""
        
        # Skip for admin, API, and static files
        if (request.path.startswith('/admin/') or 
            request.path.startswith('/api/') or 
            request.path.startswith('/static/') or
            request.path.startswith('/media/')):
            return None
        
        # Get current language
        current_language = getattr(request, 'LANGUAGE_CODE', 
                                 getattr(settings, 'DEFAULT_CONTENT_LANGUAGE', 'en'))
        
        # Check if URL already has language prefix
        path_parts = request.path.strip('/').split('/')
        if path_parts and path_parts[0] in [lang[0] for lang in settings.LANGUAGES]:
            return None
        
        # If Turkish language is detected and we want to redirect to Turkish URLs
        if current_language == 'tr' and hasattr(settings, 'USE_LANGUAGE_PREFIX_URLS'):
            if getattr(settings, 'USE_LANGUAGE_PREFIX_URLS', False):
                new_path = f'/tr{request.path}'
                return HttpResponseRedirect(new_path)
        
        return None


class APILanguageNegotiationMixin:
    """
    Mixin for API views to handle language negotiation.
    """

    def get_language_from_request(self, request):
        """
        Get language from request with API-specific priority.

        Priority:
        1. URL parameter (?lang=tr)
        2. Custom header (X-Language)
        3. Accept-Language header
        4. Default language
        """

        # 1. URL parameter (highest priority for API)
        language = request.query_params.get('lang') if hasattr(request, 'query_params') else request.GET.get('lang')
        if language and self.is_valid_language(language):
            return language

        # 2. Custom language header
        language = request.META.get('HTTP_X_LANGUAGE')
        if language and self.is_valid_language(language):
            return language

        # 3. Accept-Language header
        accept_language = request.META.get('HTTP_ACCEPT_LANGUAGE', '')
        if accept_language:
            language = self.parse_accept_language_header(accept_language)
            if language:
                return language

        # 4. Default language
        from django.conf import settings
        return getattr(settings, 'DEFAULT_CONTENT_LANGUAGE', 'en')

    def is_valid_language(self, language):
        """Check if language is supported."""
        from django.conf import settings
        supported_languages = [lang[0] for lang in settings.LANGUAGES]
        return language in supported_languages

    def parse_accept_language_header(self, accept_language):
        """Parse Accept-Language header and return best match."""
        import re

        # Parse Accept-Language header
        accept_language_re = re.compile(
            r'([a-z]{1,8}(?:-[a-z]{1,8})?)\s*(?:;\s*q\s*=\s*(1|0\.[0-9]+))?',
            re.IGNORECASE
        )

        languages = []
        for match in accept_language_re.finditer(accept_language):
            lang = match.group(1).lower()
            quality = match.group(2)

            if quality:
                try:
                    quality = float(quality)
                except ValueError:
                    quality = 1.0
            else:
                quality = 1.0

            languages.append((lang, quality))

        # Sort by quality (highest first)
        languages.sort(key=lambda x: x[1], reverse=True)

        # Find the best matching supported language
        from django.conf import settings
        supported_languages = [lang[0] for lang in settings.LANGUAGES]

        for lang, quality in languages:
            # Check exact match first
            if lang in supported_languages:
                return lang

            # Check language prefix (e.g., 'en-US' -> 'en')
            lang_prefix = lang.split('-')[0]
            if lang_prefix in supported_languages:
                return lang_prefix

        return None

    def set_language_context(self, request, language):
        """Set language context for the request."""
        from core.routers import set_current_language
        from django.utils import translation

        # Set Django's language context
        translation.activate(language)
        request.LANGUAGE_CODE = language

        # Set our custom language context for database routing
        set_current_language(language)

        # Store language in request for serializers
        request.detected_language = language
        request.api_language = language
