"""
Turkish admin interface for content models.

This admin configuration is specifically for Turkish models and provides
a Turkish-language interface for managing Turkish content.
"""

from django.contrib import admin
from django.utils.html import format_html
from .models_tr import AboutUsTR, ServiceTR, AdditionalServiceTR, FeaturedResourceTR, SocialMediaLinkTR


@admin.register(AboutUsTR)
class AboutUsTRAdmin(admin.ModelAdmin):
    """Turkish admin interface for AboutUsTR model."""

    list_display = ['title', 'company_name', 'established_year', 'is_active', 'display_order', 'created_at']
    list_filter = ['is_active', 'established_year', 'created_at']
    search_fields = ['title', 'company_name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['display_order', '-created_at']

    fieldsets = (
        ('Temel Bilgiler', {
            'fields': ('title', 'company_name', 'established_year', 'description')
        }),
        ('Görseller', {
            'fields': ('main_image', 'secondary_image')
        }),
        ('Şirket Detayları', {
            'fields': ('mission_statement', 'vision_statement', 'values')
        }),
        ('Görüntüleme Ayarları', {
            'fields': ('is_active', 'display_order')
        }),
        ('Zaman Damgaları', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    class Meta:
        verbose_name = 'Hakkımızda'
        verbose_name_plural = 'Hakkımızda'


@admin.register(ServiceTR)
class ServiceTRAdmin(admin.ModelAdmin):
    """Turkish admin interface for ServiceTR model."""

    list_display = ['title', 'category', 'price_range', 'delivery_time', 'is_featured', 'is_active', 'display_order']
    list_filter = ['category', 'is_featured', 'is_active', 'created_at']
    search_fields = ['title', 'short_description', 'full_description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['display_order', 'title']

    fieldsets = (
        ('Temel Bilgiler', {
            'fields': ('title', 'category', 'short_description', 'full_description')
        }),
        ('Görsel Öğeler', {
            'fields': ('icon', 'image')
        }),
        ('Hizmet Detayları', {
            'fields': ('features', 'price_range', 'delivery_time')
        }),
        ('Görüntüleme Ayarları', {
            'fields': ('is_featured', 'is_active', 'display_order')
        }),
        ('Zaman Damgaları', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_as_featured', 'mark_as_not_featured', 'activate_services', 'deactivate_services']

    def mark_as_featured(self, request, queryset):
        """Seçili hizmetleri öne çıkan olarak işaretle."""
        updated = queryset.update(is_featured=True)
        self.message_user(request, f'{updated} hizmet öne çıkan olarak işaretlendi.')
    mark_as_featured.short_description = "Seçili hizmetleri öne çıkan olarak işaretle"

    def mark_as_not_featured(self, request, queryset):
        """Seçili hizmetleri öne çıkan olmaktan çıkar."""
        updated = queryset.update(is_featured=False)
        self.message_user(request, f'{updated} hizmet öne çıkan olmaktan çıkarıldı.')
    mark_as_not_featured.short_description = "Seçili hizmetleri öne çıkan olmaktan çıkar"

    def activate_services(self, request, queryset):
        """Seçili hizmetleri aktif et."""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} hizmet aktif edildi.')
    activate_services.short_description = "Seçili hizmetleri aktif et"

    def deactivate_services(self, request, queryset):
        """Seçili hizmetleri pasif et."""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} hizmet pasif edildi.')
    deactivate_services.short_description = "Seçili hizmetleri pasif et"

    class Meta:
        verbose_name = 'Hizmet'
        verbose_name_plural = 'Hizmetler'


@admin.register(AdditionalServiceTR)
class AdditionalServiceTRAdmin(admin.ModelAdmin):
    """Turkish admin interface for AdditionalServiceTR model."""

    list_display = ['title', 'subtitle', 'is_popular', 'is_active', 'display_order']
    list_filter = ['is_popular', 'is_active', 'created_at']
    search_fields = ['title', 'subtitle', 'description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['display_order', 'title']

    fieldsets = (
        ('Temel Bilgiler', {
            'fields': ('title', 'subtitle', 'description')
        }),
        ('Görsel Öğeler', {
            'fields': ('icon', 'image')
        }),
        ('Özellikler', {
            'fields': ('features', 'price_info')
        }),
        ('Görüntüleme Ayarları', {
            'fields': ('is_popular', 'is_active', 'display_order')
        }),
        ('Zaman Damgaları', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_as_popular', 'mark_as_not_popular']

    def mark_as_popular(self, request, queryset):
        """Seçili ek hizmetleri popüler olarak işaretle."""
        updated = queryset.update(is_popular=True)
        self.message_user(request, f'{updated} ek hizmet popüler olarak işaretlendi.')
    mark_as_popular.short_description = "Seçili ek hizmetleri popüler olarak işaretle"

    def mark_as_not_popular(self, request, queryset):
        """Seçili ek hizmetleri popüler olmaktan çıkar."""
        updated = queryset.update(is_popular=False)
        self.message_user(request, f'{updated} ek hizmet popüler olmaktan çıkarıldı.')
    mark_as_not_popular.short_description = "Seçili ek hizmetleri popüler olmaktan çıkar"

    class Meta:
        verbose_name = 'Ek Hizmet'
        verbose_name_plural = 'Ek Hizmetler'


@admin.register(FeaturedResourceTR)
class FeaturedResourceTRAdmin(admin.ModelAdmin):
    """Turkish admin interface for FeaturedResourceTR model."""

    list_display = [
        'title', 'resource_type', 'author', 'difficulty_level',
        'view_count', 'is_featured', 'is_active', 'published_date'
    ]
    list_filter = [
        'resource_type', 'difficulty_level', 'is_featured',
        'is_active', 'external_link', 'published_date', 'created_at'
    ]
    search_fields = ['title', 'description', 'author']
    readonly_fields = ['created_at', 'updated_at', 'view_count']
    ordering = ['display_order', '-published_date']
    date_hierarchy = 'published_date'

    fieldsets = (
        ('Temel Bilgiler', {
            'fields': ('title', 'description', 'author', 'published_date')
        }),
        ('İçerik Detayları', {
            'fields': ('resource_type', 'difficulty_level', 'reading_time', 'tags')
        }),
        ('Bağlantılar ve Medya', {
            'fields': ('content_url', 'external_link', 'image_url')
        }),
        ('Görüntüleme Ayarları', {
            'fields': ('is_featured', 'is_active', 'display_order')
        }),
        ('Analitik', {
            'fields': ('view_count',),
            'classes': ('collapse',)
        }),
        ('Zaman Damgaları', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_as_featured', 'mark_as_not_featured', 'reset_view_count']

    def mark_as_featured(self, request, queryset):
        """Seçili kaynakları öne çıkan olarak işaretle."""
        updated = queryset.update(is_featured=True)
        self.message_user(request, f'{updated} kaynak öne çıkan olarak işaretlendi.')
    mark_as_featured.short_description = "Seçili kaynakları öne çıkan olarak işaretle"

    def mark_as_not_featured(self, request, queryset):
        """Seçili kaynakları öne çıkan olmaktan çıkar."""
        updated = queryset.update(is_featured=False)
        self.message_user(request, f'{updated} kaynak öne çıkan olmaktan çıkarıldı.')
    mark_as_not_featured.short_description = "Seçili kaynakları öne çıkan olmaktan çıkar"

    def reset_view_count(self, request, queryset):
        """Seçili kaynakların görüntülenme sayısını sıfırla."""
        updated = queryset.update(view_count=0)
        self.message_user(request, f'{updated} kaynağın görüntülenme sayısı sıfırlandı.')
    reset_view_count.short_description = "Görüntülenme sayısını sıfırla"

    class Meta:
        verbose_name = 'Öne Çıkan Kaynak'
        verbose_name_plural = 'Öne Çıkan Kaynaklar'


@admin.register(SocialMediaLinkTR)
class SocialMediaLinkTRAdmin(admin.ModelAdmin):
    """Turkish admin interface for SocialMediaLinkTR model."""

    list_display = [
        'platform', 'display_name', 'formatted_follower_count',
        'is_active', 'display_order'
    ]
    list_filter = ['platform', 'is_active', 'created_at']
    search_fields = ['platform', 'display_name', 'url']
    readonly_fields = ['created_at', 'updated_at', 'formatted_follower_count']
    ordering = ['display_order', 'platform']

    fieldsets = (
        ('Platform Bilgileri', {
            'fields': ('platform', 'display_name', 'url')
        }),
        ('Görsel Öğeler', {
            'fields': ('icon_class', 'icon_svg')
        }),
        ('Analitik', {
            'fields': ('follower_count', 'formatted_follower_count')
        }),
        ('Görüntüleme Ayarları', {
            'fields': ('is_active', 'display_order')
        }),
        ('Zaman Damgaları', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def formatted_follower_count(self, obj):
        """Biçimlendirilmiş takipçi sayısını göster."""
        return obj.formatted_follower_count or 'Belirtilmemiş'
    formatted_follower_count.short_description = 'Takipçi Sayısı'

    class Meta:
        verbose_name = 'Sosyal Medya Bağlantısı'
        verbose_name_plural = 'Sosyal Medya Bağlantıları'
