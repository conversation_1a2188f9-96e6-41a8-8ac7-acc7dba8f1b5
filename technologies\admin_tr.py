"""
Turkish admin interface for technology models.

This admin configuration is specifically for Turkish technology models and provides
a Turkish-language interface for managing Turkish technology content.
"""

from django.contrib import admin
from django.utils.html import format_html
from .models_tr import TechnologyCategoryTR, TechnologyTR


class TechnologyTRInline(admin.TabularInline):
    """Inline admin for Turkish technologies within a category."""
    model = TechnologyTR
    extra = 0
    fields = ['name', 'proficiency_level', 'years_experience', 'is_featured', 'is_active', 'display_order']
    ordering = ['display_order', 'name']
    verbose_name = 'Teknoloji'
    verbose_name_plural = 'Teknolojiler'


@admin.register(TechnologyCategoryTR)
class TechnologyCategoryTRAdmin(admin.ModelAdmin):
    """Turkish admin interface for TechnologyCategoryTR model."""

    list_display = ['display_name', 'name', 'technology_count', 'is_active', 'display_order', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'display_name', 'description']
    readonly_fields = ['created_at', 'updated_at', 'technology_count', 'featured_technology_count']
    ordering = ['display_order', 'display_name']
    inlines = [TechnologyTRInline]

    fieldsets = (
        ('Temel Bilgiler', {
            'fields': ('name', 'display_name', 'description')
        }),
        ('Görsel Öğeler', {
            'fields': ('icon', 'color_code')
        }),
        ('Görüntüleme Ayarları', {
            'fields': ('is_active', 'display_order')
        }),
        ('İstatistikler', {
            'fields': ('technology_count', 'featured_technology_count'),
            'classes': ('collapse',)
        }),
        ('Zaman Damgaları', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['activate_categories', 'deactivate_categories']

    def technology_count(self, obj):
        """Bu kategorideki teknoloji sayısını göster."""
        count = obj.technology_count
        if count > 0:
            return format_html('<span style="color: green;">{} teknoloji</span>', count)
        return format_html('<span style="color: orange;">Teknoloji yok</span>')
    technology_count.short_description = 'Teknoloji Sayısı'

    def featured_technology_count(self, obj):
        """Bu kategorideki öne çıkan teknoloji sayısını göster."""
        count = obj.featured_technology_count
        if count > 0:
            return format_html('<span style="color: blue;">{} öne çıkan</span>', count)
        return format_html('<span style="color: gray;">Öne çıkan yok</span>')
    featured_technology_count.short_description = 'Öne Çıkan Teknoloji'

    def activate_categories(self, request, queryset):
        """Seçili kategorileri aktif et."""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} kategori aktif edildi.')
    activate_categories.short_description = "Seçili kategorileri aktif et"

    def deactivate_categories(self, request, queryset):
        """Seçili kategorileri pasif et."""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} kategori pasif edildi.')
    deactivate_categories.short_description = "Seçili kategorileri pasif et"

    class Meta:
        verbose_name = 'Teknoloji Kategorisi'
        verbose_name_plural = 'Teknoloji Kategorileri'


@admin.register(TechnologyTR)
class TechnologyTRAdmin(admin.ModelAdmin):
    """Turkish admin interface for TechnologyTR model."""

    list_display = [
        'name', 'category', 'proficiency_level', 'years_experience',
        'proficiency_percentage', 'is_featured', 'is_active', 'display_order'
    ]
    list_filter = [
        'category', 'proficiency_level', 'is_featured',
        'is_active', 'years_experience', 'created_at'
    ]
    search_fields = ['name', 'description', 'category__name', 'category__display_name']
    readonly_fields = [
        'created_at', 'updated_at', 'proficiency_percentage', 
        'experience_level', 'proficiency_color', 'proficiency_icon'
    ]
    ordering = ['category__display_order', 'display_order', 'name']

    fieldsets = (
        ('Temel Bilgiler', {
            'fields': ('name', 'category', 'description', 'logo_url')
        }),
        ('Yeterlilik ve Deneyim', {
            'fields': (
                'proficiency_level', 'years_experience', 'proficiency_percentage', 
                'experience_level', 'proficiency_color', 'proficiency_icon'
            )
        }),
        ('Görüntüleme Ayarları', {
            'fields': ('is_featured', 'is_active', 'display_order')
        }),
        ('Zaman Damgaları', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = [
        'mark_as_featured', 'mark_as_not_featured', 
        'set_intermediate_proficiency', 'set_advanced_proficiency'
    ]

    def proficiency_percentage(self, obj):
        """Yeterliliği görsel yüzde olarak göster."""
        percentage = obj.proficiency_percentage
        color = 'red' if percentage < 50 else 'orange' if percentage < 75 else 'green'
        return format_html(
            '<div style="width: 100px; background-color: #f0f0f0; border-radius: 3px;">'
            '<div style="width: {}%; background-color: {}; height: 20px; border-radius: 3px; '
            'text-align: center; color: white; font-size: 12px; line-height: 20px;">'
            '{}%</div></div>',
            percentage, color, percentage
        )
    proficiency_percentage.short_description = 'Yeterlilik Yüzdesi'

    def experience_level(self, obj):
        """Deneyim seviyesini göster."""
        return obj.experience_level
    experience_level.short_description = 'Deneyim Seviyesi'

    def proficiency_color(self, obj):
        """Yeterlilik rengini göster."""
        color = obj.proficiency_color
        return format_html(
            '<div style="width: 30px; height: 20px; background-color: {}; border: 1px solid #ccc; border-radius: 3px;"></div>',
            color
        )
    proficiency_color.short_description = 'Yeterlilik Rengi'

    def proficiency_icon(self, obj):
        """Yeterlilik ikonunu göster."""
        icon = obj.proficiency_icon
        return format_html('<i class="{}"></i> {}', icon, icon)
    proficiency_icon.short_description = 'Yeterlilik İkonu'

    def mark_as_featured(self, request, queryset):
        """Seçili teknolojileri öne çıkan olarak işaretle."""
        updated = queryset.update(is_featured=True)
        self.message_user(request, f'{updated} teknoloji öne çıkan olarak işaretlendi.')
    mark_as_featured.short_description = "Seçili teknolojileri öne çıkan olarak işaretle"

    def mark_as_not_featured(self, request, queryset):
        """Seçili teknolojileri öne çıkan olmaktan çıkar."""
        updated = queryset.update(is_featured=False)
        self.message_user(request, f'{updated} teknoloji öne çıkan olmaktan çıkarıldı.')
    mark_as_not_featured.short_description = "Seçili teknolojileri öne çıkan olmaktan çıkar"

    def set_intermediate_proficiency(self, request, queryset):
        """Yeterliliği orta seviyeye ayarla."""
        updated = queryset.update(proficiency_level='intermediate')
        self.message_user(request, f'{updated} teknolojinin yeterliliği orta seviyeye ayarlandı.')
    set_intermediate_proficiency.short_description = "Yeterliliği orta seviyeye ayarla"

    def set_advanced_proficiency(self, request, queryset):
        """Yeterliliği ileri seviyeye ayarla."""
        updated = queryset.update(proficiency_level='advanced')
        self.message_user(request, f'{updated} teknolojinin yeterliliği ileri seviyeye ayarlandı.')
    set_advanced_proficiency.short_description = "Yeterliliği ileri seviyeye ayarla"

    class Meta:
        verbose_name = 'Teknoloji'
        verbose_name_plural = 'Teknolojiler'
