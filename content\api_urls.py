"""
API URL routing for content models with multi-language support.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .viewsets import (
    AboutUsViewSet, ServiceViewSet, AdditionalServiceViewSet,
    FeaturedResourceViewSet, SocialMediaLinkViewSet, ContentSummaryViewSet
)

# Create router for content API endpoints
router = DefaultRouter()
router.register(r'about-us', AboutUsViewSet, basename='about-us')
router.register(r'services', ServiceViewSet, basename='services')
router.register(r'additional-services', AdditionalServiceViewSet, basename='additional-services')
router.register(r'featured-resources', FeaturedResourceViewSet, basename='featured-resources')
router.register(r'social-media-links', SocialMediaLinkViewSet, basename='social-media-links')
router.register(r'summary', ContentSummaryViewSet, basename='content-summary')

app_name = 'content_api'

urlpatterns = [
    path('', include(router.urls)),
]
