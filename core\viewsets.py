"""
Base ViewSets for multi-language API support.
"""

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db import models
from .api_utils import (
    ContentNegotiationMixin, MultiLanguageResponseMixin, 
    LanguageHeaderMixin, create_language_aware_response,
    get_model_for_request, validate_language_parameter,
    get_available_languages
)
from .routers import LanguageContext, get_current_language
from .models import ModelFactory


class LanguageAwareViewSet(ContentNegotiationMixin, LanguageHeaderMixin, 
                          viewsets.ModelViewSet):
    """
    Base ViewSet that automatically handles language-specific models.
    """
    
    # Override these in subclasses
    base_model_name = None
    base_serializer_class = None
    list_serializer_class = None
    
    def dispatch(self, request, *args, **kwargs):
        """Set up language context before processing request."""
        self.setup_language_context(request)
        return super().dispatch(request, *args, **kwargs)
    
    def get_queryset(self):
        """Get queryset for the appropriate language model."""
        if not self.base_model_name:
            return super().get_queryset()
        
        try:
            language = get_current_language()
            model_class = ModelFactory.get_model(self.base_model_name, language)
            
            # Get base queryset
            queryset = model_class.objects.all()
            
            # Apply common filters
            if hasattr(model_class, 'is_active'):
                # Only show active items by default
                if not self.request.query_params.get('include_inactive'):
                    queryset = queryset.filter(is_active=True)
            
            if hasattr(model_class, 'display_order'):
                queryset = queryset.order_by('display_order')
            
            return queryset
            
        except ValueError as e:
            # If model doesn't exist for this language, return empty queryset
            return self.queryset.none() if hasattr(self, 'queryset') else models.QuerySet().none()
    
    def get_serializer_class(self):
        """Get appropriate serializer class."""
        if self.action == 'list' and self.list_serializer_class:
            return self.list_serializer_class
        
        return self.base_serializer_class or super().get_serializer_class()
    
    def get_serializer_context(self):
        """Add language context to serializer."""
        context = super().get_serializer_context()
        context['language'] = get_current_language()
        return context
    
    @action(detail=False, methods=['get'])
    def languages(self, request):
        """Get available languages for this resource."""
        return Response(get_available_languages())
    
    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get summary statistics for this resource."""
        try:
            language = get_current_language()
            model_class = ModelFactory.get_model(self.base_model_name, language)
            
            total_count = model_class.objects.count()
            active_count = model_class.objects.filter(is_active=True).count() if hasattr(model_class, 'is_active') else total_count
            
            summary_data = {
                'total_count': total_count,
                'active_count': active_count,
                'language': language,
                'model_name': self.base_model_name
            }
            
            return create_language_aware_response(summary_data)
            
        except Exception as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def multi_language(self, request):
        """Get data in multiple languages."""
        languages = request.query_params.get('languages', 'en,tr').split(',')
        
        # Validate languages
        for lang in languages:
            is_valid, error_msg = validate_language_parameter(lang.strip())
            if not is_valid:
                return Response(
                    {'error': error_msg}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        multi_lang_data = {}
        
        for language in languages:
            language = language.strip()
            with LanguageContext(language):
                try:
                    queryset = self.get_queryset()
                    serializer = self.get_serializer(queryset, many=True)
                    multi_lang_data[language] = serializer.data
                except Exception as e:
                    multi_lang_data[language] = {'error': str(e)}
        
        return Response({
            'data': multi_lang_data,
            'meta': {
                'languages': languages,
                'supported_languages': [lang[0] for lang in get_available_languages()]
            }
        })


class ReadOnlyLanguageAwareViewSet(ContentNegotiationMixin, LanguageHeaderMixin,
                                  viewsets.ReadOnlyModelViewSet):
    """
    Read-only ViewSet for language-aware models.
    """
    
    base_model_name = None
    base_serializer_class = None
    list_serializer_class = None
    
    def dispatch(self, request, *args, **kwargs):
        """Set up language context before processing request."""
        self.setup_language_context(request)
        return super().dispatch(request, *args, **kwargs)
    
    def get_queryset(self):
        """Get queryset for the appropriate language model."""
        if not self.base_model_name:
            return super().get_queryset()
        
        try:
            language = get_current_language()
            model_class = ModelFactory.get_model(self.base_model_name, language)
            
            queryset = model_class.objects.all()
            
            # Apply common filters
            if hasattr(model_class, 'is_active'):
                if not self.request.query_params.get('include_inactive'):
                    queryset = queryset.filter(is_active=True)
            
            if hasattr(model_class, 'display_order'):
                queryset = queryset.order_by('display_order')
            
            return queryset
            
        except ValueError:
            return models.QuerySet().none()
    
    def get_serializer_class(self):
        """Get appropriate serializer class."""
        if self.action == 'list' and self.list_serializer_class:
            return self.list_serializer_class
        
        return self.base_serializer_class or super().get_serializer_class()
    
    def get_serializer_context(self):
        """Add language context to serializer."""
        context = super().get_serializer_context()
        context['language'] = get_current_language()
        return context


class MultiLanguageViewSet(MultiLanguageResponseMixin, LanguageHeaderMixin,
                          viewsets.ViewSet):
    """
    ViewSet that provides data in multiple languages simultaneously.
    """
    
    base_model_name = None
    base_serializer_class = None
    
    def list(self, request):
        """List items in all languages."""
        languages = request.query_params.get('languages', 'en,tr').split(',')
        languages = [lang.strip() for lang in languages]
        
        # Validate languages
        for lang in languages:
            is_valid, error_msg = validate_language_parameter(lang)
            if not is_valid:
                return Response(
                    {'error': error_msg}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        return self.get_multi_language_response(None, languages)
    
    def retrieve(self, request, pk=None):
        """Retrieve item in all languages."""
        languages = request.query_params.get('languages', 'en,tr').split(',')
        languages = [lang.strip() for lang in languages]
        
        multi_lang_data = {}
        
        for language in languages:
            with LanguageContext(language):
                try:
                    model_class = ModelFactory.get_model(self.base_model_name, language)
                    instance = model_class.objects.get(pk=pk)
                    serializer = self.base_serializer_class(instance, context={'language': language})
                    multi_lang_data[language] = serializer.data
                except model_class.DoesNotExist:
                    multi_lang_data[language] = {'error': 'Not found'}
                except Exception as e:
                    multi_lang_data[language] = {'error': str(e)}
        
        return Response(multi_lang_data)


class LanguageSwitchMixin:
    """
    Mixin to add language switching capabilities to ViewSets.
    """
    
    @action(detail=False, methods=['post'])
    def switch_language(self, request):
        """Switch language context for subsequent requests."""
        language = request.data.get('language')
        
        if not language:
            return Response(
                {'error': 'Language parameter is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        is_valid, error_msg = validate_language_parameter(language)
        if not is_valid:
            return Response(
                {'error': error_msg}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Set language in session or cookie
        request.session['language'] = language
        
        response_data = {
            'message': f'Language switched to {language}',
            'language': language,
            'database': 'turkish' if language == 'tr' else 'default'
        }
        
        response = create_language_aware_response(response_data)
        response.set_cookie('bean_language', language, max_age=60*60*24*365)  # 1 year
        
        return response


class DatabaseInfoViewSet(viewsets.ViewSet):
    """
    ViewSet to provide information about the multi-database setup.
    """
    
    def list(self, request):
        """Get database information."""
        from django.db import connections
        
        database_info = {}
        
        for alias in connections:
            connection = connections[alias]
            database_info[alias] = {
                'engine': connection.settings_dict.get('ENGINE', ''),
                'name': connection.settings_dict.get('NAME', ''),
                'host': connection.settings_dict.get('HOST', ''),
                'port': connection.settings_dict.get('PORT', ''),
            }
        
        return Response({
            'databases': database_info,
            'current_language': get_current_language(),
            'current_database': 'turkish' if get_current_language() == 'tr' else 'default',
            'supported_languages': get_available_languages()
        })
    
    @action(detail=False, methods=['get'])
    def health(self, request):
        """Check database health."""
        from django.db import connections
        
        health_status = {}
        
        for alias in connections:
            try:
                connection = connections[alias]
                with connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                health_status[alias] = {'status': 'healthy', 'error': None}
            except Exception as e:
                health_status[alias] = {'status': 'unhealthy', 'error': str(e)}
        
        overall_status = 'healthy' if all(
            db['status'] == 'healthy' for db in health_status.values()
        ) else 'unhealthy'
        
        return Response({
            'overall_status': overall_status,
            'databases': health_status,
            'timestamp': timezone.now().isoformat()
        })


# Import timezone for health check
from django.utils import timezone
