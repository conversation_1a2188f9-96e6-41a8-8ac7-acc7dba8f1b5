"""
Base models and utilities for multi-language support.

This module contains abstract base classes and utilities that are shared
across all Turkish models to ensure consistent behavior and database routing.
"""

from django.db import models
from django.utils import timezone


class TurkishBaseModel(models.Model):
    """
    Abstract base model for all Turkish content models.

    This ensures that all Turkish models are automatically routed to the
    Turkish database and have consistent metadata.
    """

    class Meta:
        abstract = True
        # This will be overridden in concrete models, but provides a default
        app_label = 'turkish_content'

    def save(self, *args, **kwargs):
        """Override save to ensure Turkish models use the Turkish database."""
        # Force using the Turkish database
        kwargs.setdefault('using', 'turkish')
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        """Override delete to ensure Turkish models use the Turkish database."""
        # Force using the Turkish database
        kwargs.setdefault('using', 'turkish')
        super().delete(*args, **kwargs)

    @classmethod
    def get_database_alias(cls):
        """Return the database alias for this model."""
        return 'turkish'


class TimestampedTurkishModel(TurkishBaseModel):
    """
    Abstract base model that includes created_at and updated_at fields
    for Turkish models.
    """

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True


class LanguageModelMixin:
    """
    Mixin that provides language-aware functionality to models.
    """

    @classmethod
    def get_for_language(cls, language='en'):
        """
        Get the appropriate model class for the given language.

        Args:
            language: Language code ('en' or 'tr')

        Returns:
            Model class for the specified language
        """
        if language == 'tr':
            # Try to get the Turkish version
            turkish_model_name = f"{cls.__name__}TR"
            try:
                from django.apps import apps
                app_label = cls._meta.app_label
                return apps.get_model(app_label, turkish_model_name)
            except LookupError:
                # If Turkish model doesn't exist, return the base model
                pass

        return cls

    @classmethod
    def objects_for_language(cls, language='en'):
        """
        Get the manager for the appropriate language model.

        Args:
            language: Language code ('en' or 'tr')

        Returns:
            Manager for the language-specific model
        """
        model_class = cls.get_for_language(language)
        return model_class.objects


class TurkishModelManager(models.Manager):
    """
    Custom manager for Turkish models that ensures proper database routing.
    """

    def get_queryset(self):
        """Return queryset using the Turkish database."""
        return super().get_queryset().using('turkish')

    def create(self, **kwargs):
        """Create object in the Turkish database."""
        return super().using('turkish').create(**kwargs)

    def bulk_create(self, objs, **kwargs):
        """Bulk create objects in the Turkish database."""
        return super().using('turkish').bulk_create(objs, **kwargs)

    def get_or_create(self, **kwargs):
        """Get or create object in the Turkish database."""
        return super().using('turkish').get_or_create(**kwargs)

    def update_or_create(self, **kwargs):
        """Update or create object in the Turkish database."""
        return super().using('turkish').update_or_create(**kwargs)


class LanguageAwareManager(models.Manager):
    """
    Manager that can work with both English and Turkish models
    based on the current language context.
    """

    def __init__(self, language=None):
        super().__init__()
        self.language = language

    def get_queryset(self):
        """Return queryset for the appropriate database."""
        from .routers import get_current_language

        # Use provided language or get from context
        language = self.language or get_current_language()

        if language == 'tr':
            return super().get_queryset().using('turkish')
        else:
            return super().get_queryset().using('default')

    def for_language(self, language):
        """Return a manager instance for a specific language."""
        return self.__class__(language=language)

    def create(self, **kwargs):
        """Create object in the appropriate database."""
        from .routers import get_current_language
        language = self.language or get_current_language()
        if language == 'tr':
            return super().using('turkish').create(**kwargs)
        else:
            return super().using('default').create(**kwargs)

    def bulk_create(self, objs, **kwargs):
        """Bulk create objects in the appropriate database."""
        from .routers import get_current_language
        language = self.language or get_current_language()
        if language == 'tr':
            return super().using('turkish').bulk_create(objs, **kwargs)
        else:
            return super().using('default').bulk_create(objs, **kwargs)

# Utility functions for working with multi-language models

def get_model_for_language(base_model_class, language):
    """
    Get the appropriate model class for a given language.

    Args:
        base_model_class: The base model class (e.g., AboutUs)
        language: Language code ('en' or 'tr')

    Returns:
        The appropriate model class for the language
    """
    if language == 'tr':
        # Try to get the Turkish version of the model
        turkish_model_name = f"{base_model_class.__name__}TR"
        try:
            from django.apps import apps
            app_label = base_model_class._meta.app_label
            return apps.get_model(app_label, turkish_model_name)
        except LookupError:
            # If Turkish model doesn't exist, fall back to base model
            pass

    return base_model_class


def get_objects_for_language(base_model_class, language):
    """
    Get the manager for the appropriate language model.

    Args:
        base_model_class: The base model class
        language: Language code ('en' or 'tr')

    Returns:
        Manager for the language-specific model
    """
    model_class = get_model_for_language(base_model_class, language)
    return model_class.objects


def sync_model_data(source_model, target_model, field_mapping=None):
    """
    Utility function to sync data between English and Turkish models.

    Args:
        source_model: Source model class
        target_model: Target model class
        field_mapping: Optional dict mapping source fields to target fields

    Returns:
        Number of objects synced
    """
    if field_mapping is None:
        field_mapping = {}

    synced_count = 0

    for source_obj in source_model.objects.all():
        # Create target object data
        target_data = {}

        for field in source_model._meta.fields:
            if field.name in ['id', 'created_at', 'updated_at']:
                continue

            source_field_name = field.name
            target_field_name = field_mapping.get(source_field_name, source_field_name)

            # Check if target model has this field
            try:
                target_model._meta.get_field(target_field_name)
                target_data[target_field_name] = getattr(source_obj, source_field_name)
            except models.FieldDoesNotExist:
                # Skip fields that don't exist in target model
                continue

        # Create or update target object
        if target_data:
            target_model.objects.get_or_create(**target_data)
            synced_count += 1

    return synced_count


class ModelFactory:
    """
    Factory class for creating language-specific model instances.
    """

    # Mapping of English models to their Turkish counterparts
    MODEL_MAPPING = {
        'AboutUs': 'AboutUsTR',
        'Service': 'ServiceTR',
        'AdditionalService': 'AdditionalServiceTR',
        'FeaturedResource': 'FeaturedResourceTR',
        'SocialMediaLink': 'SocialMediaLinkTR',
        'TeamMember': 'TeamMemberTR',
        'TechnologyCategory': 'TechnologyCategoryTR',
        'Technology': 'TechnologyTR',
        'DemoRequest': 'DemoRequestTR',
    }

    @classmethod
    def get_model(cls, model_name, language='en'):
        """
        Get model class by name and language.

        Args:
            model_name: Name of the base model (e.g., 'AboutUs')
            language: Language code ('en' or 'tr')

        Returns:
            Model class for the specified language
        """
        if language == 'tr' and model_name in cls.MODEL_MAPPING:
            turkish_model_name = cls.MODEL_MAPPING[model_name]

            # Try to import the Turkish model
            try:
                from django.apps import apps

                # Determine the app label based on model name
                app_mapping = {
                    'AboutUsTR': 'content',
                    'ServiceTR': 'content',
                    'AdditionalServiceTR': 'content',
                    'FeaturedResourceTR': 'content',
                    'SocialMediaLinkTR': 'content',
                    'TeamMemberTR': 'team',
                    'TechnologyCategoryTR': 'technologies',
                    'TechnologyTR': 'technologies',
                    'DemoRequestTR': 'demos',
                }

                app_label = app_mapping.get(turkish_model_name)
                if app_label:
                    return apps.get_model(app_label, turkish_model_name)

            except LookupError:
                pass

        # Fall back to English model or try to get it from apps
        try:
            from django.apps import apps

            # Determine app label for English models
            app_mapping = {
                'AboutUs': 'content',
                'Service': 'content',
                'AdditionalService': 'content',
                'FeaturedResource': 'content',
                'SocialMediaLink': 'content',
                'TeamMember': 'team',
                'TechnologyCategory': 'technologies',
                'Technology': 'technologies',
                'DemoRequest': 'demos',
            }

            app_label = app_mapping.get(model_name)
            if app_label:
                return apps.get_model(app_label, model_name)

        except LookupError:
            pass

        raise ValueError(f"Model '{model_name}' not found for language '{language}'")

    @classmethod
    def create_instance(cls, model_name, language='en', **kwargs):
        """
        Create a model instance for the specified language.

        Args:
            model_name: Name of the base model
            language: Language code ('en' or 'tr')
            **kwargs: Model field values

        Returns:
            Created model instance
        """
        model_class = cls.get_model(model_name, language)
        return model_class.objects.create(**kwargs)

    @classmethod
    def get_queryset(cls, model_name, language='en'):
        """
        Get queryset for the specified model and language.

        Args:
            model_name: Name of the base model
            language: Language code ('en' or 'tr')

        Returns:
            QuerySet for the model
        """
        model_class = cls.get_model(model_name, language)
        return model_class.objects.all()

    @classmethod
    def get_all_models_for_language(cls, language='en'):
        """
        Get all model classes for a specific language.

        Args:
            language: Language code ('en' or 'tr')

        Returns:
            Dictionary mapping model names to model classes
        """
        models = {}

        if language == 'tr':
            model_names = cls.MODEL_MAPPING.keys()
        else:
            model_names = cls.MODEL_MAPPING.keys()

        for model_name in model_names:
            try:
                models[model_name] = cls.get_model(model_name, language)
            except ValueError:
                continue

        return models

