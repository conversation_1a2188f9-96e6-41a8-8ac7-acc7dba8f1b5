"""
Database routing for multi-language support.

This router automatically directs Turkish models to the Turkish database
and English models to the default database, ensuring compliance with
Turkish data storage laws.
"""

import threading
from django.conf import settings


# Thread-local storage for language context
_thread_local = threading.local()


def get_current_language():
    """Get the current language from thread-local storage."""
    return getattr(_thread_local, 'language', 'en')


def set_current_language(language):
    """Set the current language in thread-local storage."""
    _thread_local.language = language


def clear_language_context():
    """Clear the language context from thread-local storage."""
    if hasattr(_thread_local, 'language'):
        delattr(_thread_local, 'language')


def get_language_context():
    """Get the complete language context information."""
    return {
        'language': get_current_language(),
        'database': get_database_for_language(get_current_language()),
        'is_turkish': get_current_language() == 'tr',
        'is_english': get_current_language() == 'en',
    }


class LanguageRouter:
    """
    A router to control all database operations on models for different languages.
    
    Turkish models (ending with 'TR') are routed to the 'turkish' database.
    All other models are routed to the 'default' database.
    """
    
    # Turkish model suffixes and app labels that should use Turkish database
    TURKISH_MODEL_SUFFIXES = ('TR', 'Turkish')
    TURKISH_APP_LABELS = ('turkish_content', 'turkish_team', 'turkish_technologies', 'turkish_demos')
    
    def db_for_read(self, model, **hints):
        """Suggest the database to read from."""
        return self._get_database_for_model(model)
    
    def db_for_write(self, model, **hints):
        """Suggest the database to write to."""
        return self._get_database_for_model(model)
    
    def allow_relation(self, obj1, obj2, **hints):
        """Allow relations if models are in the same database."""
        db_set = {'default', 'turkish'}
        if obj1._state.db in db_set and obj2._state.db in db_set:
            return True
        return None
    
    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """Ensure that certain apps' models get created on the right database."""

        # Turkish models should only be created in the Turkish database
        if model_name and self._is_turkish_model(model_name, app_label):
            return db == 'turkish'

        # Turkish app labels should only be in Turkish database
        if app_label in self.TURKISH_APP_LABELS:
            return db == 'turkish'

        # Django's built-in apps should only be in the default database
        django_apps = ['admin', 'auth', 'contenttypes', 'sessions', 'messages']
        if app_label in django_apps:
            return db == 'default'

        # All other models should only be created in the default database
        if db == 'turkish':
            return False

        return db == 'default'
    
    def _get_database_for_model(self, model):
        """Determine which database to use for a given model."""
        model_name = model.__name__
        app_label = model._meta.app_label
        
        # Check if it's a Turkish model
        if self._is_turkish_model(model_name, app_label):
            return 'turkish'
        
        # Check if it's a Turkish app
        if app_label in self.TURKISH_APP_LABELS:
            return 'turkish'
        
        # Default to the default database
        return 'default'
    
    def _is_turkish_model(self, model_name, app_label):
        """Check if a model should use the Turkish database."""
        if not model_name:
            return False
        
        # Check if model name ends with Turkish suffixes
        for suffix in self.TURKISH_MODEL_SUFFIXES:
            if model_name.endswith(suffix):
                return True
        
        return False


class DatabaseManager:
    """
    Utility class for managing database operations across multiple databases.
    """
    
    @staticmethod
    def get_database_for_language(language):
        """Get the appropriate database alias for a given language."""
        if language == 'tr':
            return 'turkish'
        return 'default'
    
    @staticmethod
    def with_language_context(language):
        """Context manager for setting language context."""
        class LanguageContext:
            def __init__(self, lang):
                self.language = lang
                self.previous_language = None
            
            def __enter__(self):
                self.previous_language = get_current_language()
                set_current_language(self.language)
                return self
            
            def __exit__(self, exc_type, exc_val, exc_tb):
                if self.previous_language:
                    set_current_language(self.previous_language)
                else:
                    # Clear the language context
                    if hasattr(_thread_local, 'language'):
                        delattr(_thread_local, 'language')
        
        return LanguageContext(language)
    
    @staticmethod
    def get_model_for_language(base_model_class, language):
        """
        Get the appropriate model class for a given language.
        
        Args:
            base_model_class: The base model class (e.g., AboutUs)
            language: Language code ('en' or 'tr')
        
        Returns:
            The appropriate model class for the language
        """
        if language == 'tr':
            # Try to get the Turkish version of the model
            turkish_model_name = f"{base_model_class.__name__}TR"
            try:
                from django.apps import apps
                app_label = base_model_class._meta.app_label
                return apps.get_model(app_label, turkish_model_name)
            except LookupError:
                # If Turkish model doesn't exist, fall back to base model
                pass
        
        return base_model_class


def route_to_database(language):
    """
    Decorator to temporarily set language context for database operations.

    Usage:
        @route_to_database('tr')
        def some_function():
            # This function will use Turkish database
            pass
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            previous_language = get_current_language()
            set_current_language(language)
            try:
                return func(*args, **kwargs)
            finally:
                set_current_language(previous_language)
        return wrapper
    return decorator


def get_database_for_language(language):
    """Get the database alias for a given language."""
    if language == 'tr':
        return 'turkish'
    return 'default'


def execute_on_database(database_alias, query, params=None):
    """
    Execute a raw SQL query on a specific database.

    Args:
        database_alias: Database alias ('default' or 'turkish')
        query: SQL query string
        params: Query parameters

    Returns:
        Query results
    """
    from django.db import connections

    connection = connections[database_alias]
    with connection.cursor() as cursor:
        cursor.execute(query, params or [])
        if query.strip().upper().startswith('SELECT'):
            return cursor.fetchall()
        return cursor.rowcount


class LanguageContext:
    """
    Context manager for temporarily switching language context.

    Usage:
        with LanguageContext('tr'):
            # Operations here will use Turkish database
            turkish_data = SomeModel.objects.all()
    """

    def __init__(self, language):
        self.language = language
        self.previous_language = None

    def __enter__(self):
        self.previous_language = get_current_language()
        set_current_language(self.language)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.previous_language:
            set_current_language(self.previous_language)
        else:
            clear_language_context()


class MultiLanguageQuerySet:
    """
    Utility class for querying data from both languages.
    """

    def __init__(self, model_name):
        self.model_name = model_name

    def get_english_data(self):
        """Get data from English database."""
        with LanguageContext('en'):
            from core.models import ModelFactory
            return ModelFactory.get_queryset(self.model_name, 'en')

    def get_turkish_data(self):
        """Get data from Turkish database."""
        with LanguageContext('tr'):
            from core.models import ModelFactory
            return ModelFactory.get_queryset(self.model_name, 'tr')

    def get_combined_data(self):
        """Get data from both databases (returns a dict)."""
        return {
            'english': list(self.get_english_data()),
            'turkish': list(self.get_turkish_data())
        }

    def count_all(self):
        """Get count from both databases."""
        return {
            'english': self.get_english_data().count(),
            'turkish': self.get_turkish_data().count()
        }
