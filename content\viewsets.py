"""
ViewSets for content models with multi-language support.
"""

from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, extend_schema_view
from core.viewsets import LanguageAwareViewSet, ReadOnlyLanguageAwareViewSet, LanguageSwitchMixin
from core.api_utils import create_language_aware_response
from core.schema import ABOUT_US_SCHEMA, SERVICES_SCHEMA, LANGUAGE_PARAMETER, LANGUAGE_HEADER
from .serializers import (
    AboutUsSerializer, AboutUsListSerializer,
    ServiceSerializer, ServiceListSerializer,
    AdditionalServiceSerializer,
    FeaturedResourceSerializer, FeaturedResourceListSerializer,
    SocialMediaLinkSerializer,
    ContentSummarySerializer
)


@extend_schema_view(
    list=ABOUT_US_SCHEMA['list'],
    retrieve=ABOUT_US_SCHEMA['retrieve'],
    featured=ABOUT_US_SCHEMA['featured']
)
class AboutUsViewSet(LanguageSwitchMixin, LanguageAwareViewSet):
    """
    ViewSet for AboutUs model with language awareness.

    Provides CRUD operations for About Us content with automatic language routing.
    Turkish content is stored in a separate database to comply with Turkish data laws.
    """

    base_model_name = 'AboutUs'
    base_serializer_class = AboutUsSerializer
    list_serializer_class = AboutUsListSerializer
    
    def get_queryset(self):
        """Get AboutUs queryset with custom filtering."""
        queryset = super().get_queryset()
        
        # Filter by company name if provided
        company_name = self.request.query_params.get('company_name')
        if company_name:
            queryset = queryset.filter(company_name__icontains=company_name)
        
        # Filter by established year range
        year_from = self.request.query_params.get('year_from')
        year_to = self.request.query_params.get('year_to')
        
        if year_from:
            try:
                queryset = queryset.filter(established_year__gte=int(year_from))
            except ValueError:
                pass
        
        if year_to:
            try:
                queryset = queryset.filter(established_year__lte=int(year_to))
            except ValueError:
                pass
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Get featured about us entries."""
        queryset = self.get_queryset().filter(is_active=True)[:1]  # Usually just one about us entry
        serializer = self.get_serializer(queryset, many=True)
        return create_language_aware_response(serializer.data)


@extend_schema_view(
    list=SERVICES_SCHEMA['list'],
    categories=SERVICES_SCHEMA['categories'],
    featured=SERVICES_SCHEMA['featured']
)
class ServiceViewSet(LanguageSwitchMixin, LanguageAwareViewSet):
    """
    ViewSet for Service model with language awareness.

    Provides comprehensive service management with filtering, search, and categorization.
    Supports both English and Turkish content with automatic database routing.
    """

    base_model_name = 'Service'
    base_serializer_class = ServiceSerializer
    list_serializer_class = ServiceListSerializer
    
    def get_queryset(self):
        """Get Service queryset with custom filtering."""
        queryset = super().get_queryset()
        
        # Filter by category
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category=category)
        
        # Filter by featured status
        featured = self.request.query_params.get('featured')
        if featured is not None:
            is_featured = featured.lower() in ['true', '1', 'yes']
            queryset = queryset.filter(is_featured=is_featured)
        
        # Search in title and description
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(title__icontains=search) |
                models.Q(short_description__icontains=search) |
                models.Q(full_description__icontains=search)
            )
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def categories(self, request):
        """Get available service categories."""
        from core.models import ModelFactory
        from core.routers import get_current_language
        
        try:
            language = get_current_language()
            model_class = ModelFactory.get_model('Service', language)
            
            # Get category choices from the model
            if hasattr(model_class, 'CATEGORY_CHOICES'):
                categories = [
                    {'value': choice[0], 'label': choice[1]} 
                    for choice in model_class.CATEGORY_CHOICES
                ]
            else:
                # Fallback: get unique categories from database
                categories = list(
                    model_class.objects.values_list('category', flat=True).distinct()
                )
                categories = [{'value': cat, 'label': cat} for cat in categories]
            
            return create_language_aware_response(categories)
            
        except Exception as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Get featured services."""
        queryset = self.get_queryset().filter(is_featured=True)
        serializer = self.get_serializer(queryset, many=True)
        return create_language_aware_response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def increment_views(self, request, pk=None):
        """Increment view count for a service (if implemented)."""
        service = self.get_object()
        
        # If the model has a view_count field, increment it
        if hasattr(service, 'view_count'):
            service.view_count += 1
            service.save(update_fields=['view_count'])
            return create_language_aware_response({'view_count': service.view_count})
        
        return Response(
            {'message': 'View count not supported for this model'}, 
            status=status.HTTP_200_OK
        )


class AdditionalServiceViewSet(LanguageSwitchMixin, LanguageAwareViewSet):
    """
    ViewSet for AdditionalService model with language awareness.
    """
    
    base_model_name = 'AdditionalService'
    base_serializer_class = AdditionalServiceSerializer
    
    def get_queryset(self):
        """Get AdditionalService queryset with custom filtering."""
        queryset = super().get_queryset()
        
        # Filter by popular status
        popular = self.request.query_params.get('popular')
        if popular is not None:
            is_popular = popular.lower() in ['true', '1', 'yes']
            queryset = queryset.filter(is_popular=is_popular)
        
        # Search in title and description
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(title__icontains=search) |
                models.Q(subtitle__icontains=search) |
                models.Q(description__icontains=search)
            )
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def popular(self, request):
        """Get popular additional services."""
        queryset = self.get_queryset().filter(is_popular=True)
        serializer = self.get_serializer(queryset, many=True)
        return create_language_aware_response(serializer.data)


class FeaturedResourceViewSet(LanguageSwitchMixin, LanguageAwareViewSet):
    """
    ViewSet for FeaturedResource model with language awareness.
    """
    
    base_model_name = 'FeaturedResource'
    base_serializer_class = FeaturedResourceSerializer
    list_serializer_class = FeaturedResourceListSerializer
    
    def get_queryset(self):
        """Get FeaturedResource queryset with custom filtering."""
        queryset = super().get_queryset()
        
        # Filter by resource type
        resource_type = self.request.query_params.get('resource_type')
        if resource_type:
            queryset = queryset.filter(resource_type=resource_type)
        
        # Filter by difficulty level
        difficulty = self.request.query_params.get('difficulty')
        if difficulty:
            queryset = queryset.filter(difficulty_level=difficulty)
        
        # Filter by author
        author = self.request.query_params.get('author')
        if author:
            queryset = queryset.filter(author__icontains=author)
        
        # Filter by featured status
        featured = self.request.query_params.get('featured')
        if featured is not None:
            is_featured = featured.lower() in ['true', '1', 'yes']
            queryset = queryset.filter(is_featured=is_featured)
        
        # Search in title and description
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(title__icontains=search) |
                models.Q(description__icontains=search)
            )
        
        # Order by published date (newest first) if not already ordered
        if not queryset.ordered:
            queryset = queryset.order_by('-published_date', '-created_at')
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def resource_types(self, request):
        """Get available resource types."""
        from core.models import ModelFactory
        from core.routers import get_current_language
        
        try:
            language = get_current_language()
            model_class = ModelFactory.get_model('FeaturedResource', language)
            
            if hasattr(model_class, 'RESOURCE_TYPE_CHOICES'):
                resource_types = [
                    {'value': choice[0], 'label': choice[1]} 
                    for choice in model_class.RESOURCE_TYPE_CHOICES
                ]
            else:
                resource_types = list(
                    model_class.objects.values_list('resource_type', flat=True).distinct()
                )
                resource_types = [{'value': rt, 'label': rt} for rt in resource_types]
            
            return create_language_aware_response(resource_types)
            
        except Exception as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def increment_views(self, request, pk=None):
        """Increment view count for a resource."""
        resource = self.get_object()
        
        if hasattr(resource, 'increment_view_count'):
            resource.increment_view_count()
        elif hasattr(resource, 'view_count'):
            resource.view_count += 1
            resource.save(update_fields=['view_count'])
        
        return create_language_aware_response({
            'view_count': getattr(resource, 'view_count', 0)
        })


class SocialMediaLinkViewSet(ReadOnlyLanguageAwareViewSet):
    """
    Read-only ViewSet for SocialMediaLink model with language awareness.
    """
    
    base_model_name = 'SocialMediaLink'
    base_serializer_class = SocialMediaLinkSerializer
    
    def get_queryset(self):
        """Get SocialMediaLink queryset with custom filtering."""
        queryset = super().get_queryset()
        
        # Filter by platform
        platform = self.request.query_params.get('platform')
        if platform:
            queryset = queryset.filter(platform=platform)
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def platforms(self, request):
        """Get available social media platforms."""
        from core.models import ModelFactory
        from core.routers import get_current_language
        
        try:
            language = get_current_language()
            model_class = ModelFactory.get_model('SocialMediaLink', language)
            
            if hasattr(model_class, 'PLATFORM_CHOICES'):
                platforms = [
                    {'value': choice[0], 'label': choice[1]} 
                    for choice in model_class.PLATFORM_CHOICES
                ]
            else:
                platforms = list(
                    model_class.objects.values_list('platform', flat=True).distinct()
                )
                platforms = [{'value': p, 'label': p} for p in platforms]
            
            return create_language_aware_response(platforms)
            
        except Exception as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ContentSummaryViewSet(ReadOnlyLanguageAwareViewSet):
    """
    ViewSet for content summary and statistics.
    """
    
    def list(self, request):
        """Get content summary."""
        serializer = ContentSummarySerializer(data={})
        serializer.is_valid()  # This will populate the data
        return create_language_aware_response(serializer.data)


# Import models for filtering
from django.db import models
