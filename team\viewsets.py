"""
ViewSets for team models with multi-language support.
"""

from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db import models
from core.viewsets import LanguageAwareViewSet, LanguageSwitchMixin
from core.api_utils import create_language_aware_response
from .serializers import (
    TeamMemberSerializer, TeamMemberListSerializer, 
    TeamMemberCardSerializer, TeamSummarySerializer
)


class TeamMemberViewSet(LanguageSwitchMixin, LanguageAwareViewSet):
    """
    ViewSet for TeamMember model with language awareness.
    """
    
    base_model_name = 'TeamMember'
    base_serializer_class = TeamMemberSerializer
    list_serializer_class = TeamMemberListSerializer
    
    def get_queryset(self):
        """Get TeamMember queryset with custom filtering."""
        queryset = super().get_queryset()
        
        # Filter by position
        position = self.request.query_params.get('position')
        if position:
            queryset = queryset.filter(position__icontains=position)
        
        # Filter by expertise (search in bio and position)
        expertise = self.request.query_params.get('expertise')
        if expertise:
            queryset = queryset.filter(
                models.Q(bio__icontains=expertise) |
                models.Q(position__icontains=expertise)
            )
        
        # Search in name, position, and bio
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(full_name__icontains=search) |
                models.Q(position__icontains=search) |
                models.Q(bio__icontains=search)
            )
        
        # Filter by social media presence
        has_social = self.request.query_params.get('has_social')
        if has_social is not None:
            if has_social.lower() in ['true', '1', 'yes']:
                queryset = queryset.filter(
                    models.Q(linkedin_url__isnull=False) |
                    models.Q(github_url__isnull=False)
                ).exclude(
                    models.Q(linkedin_url='') & models.Q(github_url='')
                )
            else:
                queryset = queryset.filter(
                    models.Q(linkedin_url__isnull=True) | models.Q(linkedin_url=''),
                    models.Q(github_url__isnull=True) | models.Q(github_url='')
                )
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Get featured team members (first 3 by display order)."""
        from core.models import ModelFactory
        from core.routers import get_current_language
        
        try:
            language = get_current_language()
            model_class = ModelFactory.get_model('TeamMember', language)
            
            if hasattr(model_class, 'get_featured_members'):
                featured_members = model_class.get_featured_members(limit=3)
            else:
                featured_members = self.get_queryset()[:3]
            
            serializer = TeamMemberCardSerializer(featured_members, many=True, context={'language': language})
            return create_language_aware_response(serializer.data)
            
        except Exception as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def positions(self, request):
        """Get unique positions in the team."""
        queryset = self.get_queryset()
        positions = list(
            queryset.values_list('position', flat=True).distinct().order_by('position')
        )
        positions = [pos for pos in positions if pos]  # Remove empty positions
        
        return create_language_aware_response(positions)
    
    @action(detail=False, methods=['get'])
    def expertise_areas(self, request):
        """Get all expertise areas from team members."""
        queryset = self.get_queryset()
        all_expertise = []
        
        for member in queryset:
            if hasattr(member, 'get_expertise_areas'):
                expertise = member.get_expertise_areas()
                all_expertise.extend(expertise)
        
        # Get unique expertise areas
        unique_expertise = list(set(all_expertise))
        unique_expertise.sort()
        
        return create_language_aware_response(unique_expertise)
    
    @action(detail=False, methods=['get'])
    def cards(self, request):
        """Get team members in card format for display."""
        queryset = self.get_queryset()
        
        # Apply pagination if needed
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = TeamMemberCardSerializer(page, many=True, context=self.get_serializer_context())
            return self.get_paginated_response(serializer.data)
        
        serializer = TeamMemberCardSerializer(queryset, many=True, context=self.get_serializer_context())
        return create_language_aware_response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def contact_info(self, request, pk=None):
        """Get contact information for a team member."""
        member = self.get_object()
        
        contact_info = {
            'full_name': member.full_name,
            'position': member.position,
            'email': member.email,
            'social_links': {}
        }
        
        if member.linkedin_url:
            contact_info['social_links']['linkedin'] = member.linkedin_url
        if member.github_url:
            contact_info['social_links']['github'] = member.github_url
        
        # Add contact methods if available
        if hasattr(member, 'contact_methods'):
            contact_info['contact_methods'] = member.contact_methods
        
        return create_language_aware_response(contact_info)
    
    @action(detail=True, methods=['get'])
    def expertise(self, request, pk=None):
        """Get expertise areas for a specific team member."""
        member = self.get_object()
        
        expertise_data = {
            'full_name': member.full_name,
            'position': member.position,
            'expertise_areas': []
        }
        
        if hasattr(member, 'get_expertise_areas'):
            expertise_data['expertise_areas'] = member.get_expertise_areas()
        
        return create_language_aware_response(expertise_data)
    
    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get team summary statistics."""
        serializer = TeamSummarySerializer(data={})
        serializer.is_valid()  # This will populate the data
        return create_language_aware_response(serializer.data)
    
    def get_serializer_class(self):
        """Get appropriate serializer based on action."""
        if self.action == 'list':
            return self.list_serializer_class
        elif self.action == 'cards':
            return TeamMemberCardSerializer
        elif self.action in ['featured']:
            return TeamMemberCardSerializer
        
        return self.base_serializer_class
    
    def perform_create(self, serializer):
        """Custom create logic if needed."""
        # Add any custom logic before saving
        super().perform_create(serializer)
    
    def perform_update(self, serializer):
        """Custom update logic if needed."""
        # Add any custom logic before saving
        super().perform_update(serializer)
    
    def perform_destroy(self, instance):
        """Custom delete logic - soft delete if is_active field exists."""
        if hasattr(instance, 'is_active'):
            # Soft delete by setting is_active to False
            instance.is_active = False
            instance.save(update_fields=['is_active'])
        else:
            # Hard delete
            super().perform_destroy(instance)


class TeamStatsViewSet(LanguageAwareViewSet):
    """
    ViewSet for team statistics and analytics.
    """
    
    base_model_name = 'TeamMember'
    
    def list(self, request):
        """Get comprehensive team statistics."""
        from core.models import ModelFactory
        from core.routers import get_current_language
        from collections import Counter
        
        try:
            language = get_current_language()
            model_class = ModelFactory.get_model('TeamMember', language)
            
            all_members = model_class.objects.all()
            active_members = all_members.filter(is_active=True)
            
            # Position distribution
            positions = [member.position for member in active_members if member.position]
            position_counts = dict(Counter(positions))
            
            # Expertise distribution
            all_expertise = []
            for member in active_members:
                if hasattr(member, 'get_expertise_areas'):
                    all_expertise.extend(member.get_expertise_areas())
            
            expertise_counts = dict(Counter(all_expertise))
            
            # Social media presence
            linkedin_count = active_members.exclude(
                models.Q(linkedin_url__isnull=True) | models.Q(linkedin_url='')
            ).count()
            
            github_count = active_members.exclude(
                models.Q(github_url__isnull=True) | models.Q(github_url='')
            ).count()
            
            stats = {
                'total_members': all_members.count(),
                'active_members': active_members.count(),
                'inactive_members': all_members.filter(is_active=False).count(),
                'position_distribution': position_counts,
                'expertise_distribution': expertise_counts,
                'social_media_presence': {
                    'linkedin': linkedin_count,
                    'github': github_count,
                    'total_with_social': active_members.filter(
                        models.Q(linkedin_url__isnull=False) | models.Q(github_url__isnull=False)
                    ).exclude(
                        models.Q(linkedin_url='') & models.Q(github_url='')
                    ).count()
                },
                'top_expertise_areas': sorted(
                    expertise_counts.items(), 
                    key=lambda x: x[1], 
                    reverse=True
                )[:10]
            }
            
            return create_language_aware_response(stats)
            
        except Exception as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
