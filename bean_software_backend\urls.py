"""
URL configuration for bean_software_backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from core.admin_sites import turkish_admin_site, english_admin_site

urlpatterns = [
    # Default admin (English)
    path('admin/', admin.site.urls),

    # Language-specific admin sites
    path('admin/tr/', turkish_admin_site.urls),
    path('admin/en/', english_admin_site.urls),

    # API endpoints
    path('api/', include('api.urls')),

    # Core URLs
    path('', include('core.urls')),
]
