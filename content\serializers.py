"""
Serializers for content models with multi-language support.
"""

from rest_framework import serializers
from core.serializers import (
    LanguageAwareSerializer, TimestampedSerializer, 
    ActiveModelSerializer, DisplayOrderSerializer,
    LanguageFieldMixin
)
from .models import AboutUs, Service, AdditionalService, FeaturedResource, SocialMediaLink


class AboutUsSerializer(LanguageAwareSerializer, TimestampedSerializer, 
                       ActiveModelSerializer, DisplayOrderSerializer):
    """
    Serializer for AboutUs model with language awareness.
    """
    
    base_model_name = 'AboutUs'
    
    class Meta:
        model = AboutUs  # This will be overridden by LanguageAwareSerializer
        fields = [
            'id', 'title', 'description', 'main_image', 'secondary_image',
            'company_name', 'established_year', 'mission_statement',
            'vision_statement', 'values', 'is_active', 'display_order',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate_established_year(self, value):
        """Validate established year is reasonable."""
        if value and (value < 1900 or value > 2100):
            raise serializers.ValidationError(
                "Established year must be between 1900 and 2100."
            )
        return value


class ServiceSerializer(LanguageAwareSerializer, TimestampedSerializer,
                       ActiveModelSerializer, DisplayOrderSerializer, LanguageFieldMixin):
    """
    Serializer for Service model with language awareness.
    """
    
    base_model_name = 'Service'
    features_list = serializers.SerializerMethodField()
    category_display = serializers.SerializerMethodField()
    
    class Meta:
        model = Service  # This will be overridden by LanguageAwareSerializer
        fields = [
            'id', 'title', 'category', 'category_display', 'short_description',
            'full_description', 'icon', 'image', 'features', 'features_list',
            'price_range', 'delivery_time', 'is_featured', 'is_active',
            'display_order', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'features_list', 'category_display']
    
    def get_features_list(self, obj):
        """Return features as a list."""
        if hasattr(obj, 'features_list'):
            return obj.features_list
        return []
    
    def get_category_display(self, obj):
        """Return human-readable category name."""
        if hasattr(obj, 'get_category_display'):
            return obj.get_category_display()
        return obj.category


class AdditionalServiceSerializer(LanguageAwareSerializer, TimestampedSerializer,
                                 ActiveModelSerializer, DisplayOrderSerializer):
    """
    Serializer for AdditionalService model with language awareness.
    """
    
    base_model_name = 'AdditionalService'
    features_list = serializers.SerializerMethodField()
    
    class Meta:
        model = AdditionalService  # This will be overridden by LanguageAwareSerializer
        fields = [
            'id', 'title', 'subtitle', 'description', 'icon', 'image',
            'features', 'features_list', 'price_info', 'is_popular',
            'is_active', 'display_order', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'features_list']
    
    def get_features_list(self, obj):
        """Return features as a list."""
        if hasattr(obj, 'features_list'):
            return obj.features_list
        return []


class FeaturedResourceSerializer(LanguageAwareSerializer, TimestampedSerializer,
                                ActiveModelSerializer, DisplayOrderSerializer):
    """
    Serializer for FeaturedResource model with language awareness.
    """
    
    base_model_name = 'FeaturedResource'
    tags_list = serializers.SerializerMethodField()
    resource_type_display = serializers.SerializerMethodField()
    difficulty_display = serializers.SerializerMethodField()
    
    class Meta:
        model = FeaturedResource  # This will be overridden by LanguageAwareSerializer
        fields = [
            'id', 'title', 'description', 'image_url', 'resource_type',
            'resource_type_display', 'content_url', 'external_link',
            'reading_time', 'difficulty_level', 'difficulty_display',
            'tags', 'tags_list', 'author', 'published_date', 'is_featured',
            'is_active', 'display_order', 'view_count', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'view_count', 'tags_list',
            'resource_type_display', 'difficulty_display'
        ]
    
    def get_tags_list(self, obj):
        """Return tags as a list."""
        if hasattr(obj, 'tags_list'):
            return obj.tags_list
        return []
    
    def get_resource_type_display(self, obj):
        """Return human-readable resource type."""
        if hasattr(obj, 'get_resource_type_display'):
            return obj.get_resource_type_display()
        return obj.resource_type
    
    def get_difficulty_display(self, obj):
        """Return human-readable difficulty level."""
        if hasattr(obj, 'get_difficulty_level_display'):
            return obj.get_difficulty_level_display()
        return obj.difficulty_level


class SocialMediaLinkSerializer(LanguageAwareSerializer, TimestampedSerializer,
                               ActiveModelSerializer, DisplayOrderSerializer):
    """
    Serializer for SocialMediaLink model with language awareness.
    """
    
    base_model_name = 'SocialMediaLink'
    platform_display = serializers.SerializerMethodField()
    formatted_follower_count = serializers.SerializerMethodField()
    
    class Meta:
        model = SocialMediaLink  # This will be overridden by LanguageAwareSerializer
        fields = [
            'id', 'platform', 'platform_display', 'display_name', 'url',
            'icon_class', 'icon_svg', 'is_active', 'display_order',
            'follower_count', 'formatted_follower_count', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'platform_display', 'formatted_follower_count'
        ]
    
    def get_platform_display(self, obj):
        """Return human-readable platform name."""
        if hasattr(obj, 'get_platform_display'):
            return obj.get_platform_display()
        return obj.platform
    
    def get_formatted_follower_count(self, obj):
        """Return formatted follower count."""
        if hasattr(obj, 'formatted_follower_count'):
            return obj.formatted_follower_count
        return None


# Simplified serializers for list views
class AboutUsListSerializer(AboutUsSerializer):
    """Simplified serializer for AboutUs list view."""
    
    class Meta(AboutUsSerializer.Meta):
        fields = [
            'id', 'title', 'company_name', 'established_year',
            'is_active', 'display_order'
        ]


class ServiceListSerializer(ServiceSerializer):
    """Simplified serializer for Service list view."""
    
    class Meta(ServiceSerializer.Meta):
        fields = [
            'id', 'title', 'category', 'category_display', 'short_description',
            'icon', 'image', 'is_featured', 'is_active', 'display_order'
        ]


class FeaturedResourceListSerializer(FeaturedResourceSerializer):
    """Simplified serializer for FeaturedResource list view."""
    
    class Meta(FeaturedResourceSerializer.Meta):
        fields = [
            'id', 'title', 'image_url', 'resource_type', 'resource_type_display',
            'reading_time', 'difficulty_level', 'author', 'published_date',
            'is_featured', 'view_count'
        ]


# Summary serializers for dashboard/overview
class ContentSummarySerializer(serializers.Serializer):
    """
    Serializer for content summary/statistics.
    """
    
    about_us_count = serializers.IntegerField()
    services_count = serializers.IntegerField()
    additional_services_count = serializers.IntegerField()
    featured_resources_count = serializers.IntegerField()
    social_media_links_count = serializers.IntegerField()
    language = serializers.CharField()
    database = serializers.CharField()
    
    def to_representation(self, instance):
        """Calculate content statistics."""
        from core.routers import get_current_language
        from core.models import ModelFactory
        
        language = get_current_language()
        
        try:
            about_us_model = ModelFactory.get_model('AboutUs', language)
            services_model = ModelFactory.get_model('Service', language)
            additional_services_model = ModelFactory.get_model('AdditionalService', language)
            featured_resources_model = ModelFactory.get_model('FeaturedResource', language)
            social_media_model = ModelFactory.get_model('SocialMediaLink', language)
            
            return {
                'about_us_count': about_us_model.objects.filter(is_active=True).count(),
                'services_count': services_model.objects.filter(is_active=True).count(),
                'additional_services_count': additional_services_model.objects.filter(is_active=True).count(),
                'featured_resources_count': featured_resources_model.objects.filter(is_active=True).count(),
                'social_media_links_count': social_media_model.objects.filter(is_active=True).count(),
                'language': language,
                'database': 'turkish' if language == 'tr' else 'default'
            }
        except Exception as e:
            return {
                'error': str(e),
                'language': language,
                'database': 'turkish' if language == 'tr' else 'default'
            }
