"""
Turkish admin interface for team models.

This admin configuration is specifically for Turkish team models and provides
a Turkish-language interface for managing Turkish team member content.
"""

from django.contrib import admin
from django.utils.html import format_html
from .models_tr import TeamMemberTR


@admin.register(TeamMemberTR)
class TeamMemberTRAdmin(admin.ModelAdmin):
    """Turkish admin interface for TeamMemberTR model."""

    list_display = [
        'full_name', 'position', 'email', 'has_social_links',
        'is_active', 'display_order', 'created_at'
    ]
    list_filter = ['is_active', 'position', 'created_at']
    search_fields = ['full_name', 'position', 'email', 'bio']
    readonly_fields = ['created_at', 'updated_at', 'initials', 'first_name', 'last_name']
    ordering = ['display_order', 'full_name']

    fieldsets = (
        ('Temel Bilgiler', {
            'fields': ('full_name', 'position', 'bio')
        }),
        ('<PERSON>letişim Bilgileri', {
            'fields': ('email', 'linkedin_url', 'github_url')
        }),
        ('<PERSON><PERSON>i', {
            'fields': ('profile_image',)
        }),
        ('Görüntü<PERSON><PERSON>ı', {
            'fields': ('is_active', 'display_order')
        }),
        ('Hesaplanan Alanlar', {
            'fields': ('first_name', 'last_name', 'initials'),
            'classes': ('collapse',)
        }),
        ('Zaman Damgaları', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['activate_members', 'deactivate_members', 'update_display_order']

    def has_social_links(self, obj):
        """Takım üyesinin sosyal medya bağlantıları olup olmadığını göster."""
        if obj.has_social_links:
            return format_html('<span style="color: green;">✓ Evet</span>')
        return format_html('<span style="color: orange;">✗ Hayır</span>')
    has_social_links.short_description = 'Sosyal Medya Bağlantıları'
    has_social_links.boolean = True

    def activate_members(self, request, queryset):
        """Seçili takım üyelerini aktif et."""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} takım üyesi aktif edildi.')
    activate_members.short_description = "Seçili takım üyelerini aktif et"

    def deactivate_members(self, request, queryset):
        """Seçili takım üyelerini pasif et."""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} takım üyesi pasif edildi.')
    deactivate_members.short_description = "Seçili takım üyelerini pasif et"

    def update_display_order(self, request, queryset):
        """Seçili takım üyelerinin görüntüleme sırasını güncelle."""
        for i, member in enumerate(queryset.order_by('display_order'), 1):
            member.display_order = i * 10
            member.save(update_fields=['display_order'])
        self.message_user(request, f'{queryset.count()} takım üyesinin görüntüleme sırası güncellendi.')
    update_display_order.short_description = "Görüntüleme sırasını yeniden düzenle"

    def first_name(self, obj):
        """Ad alanını göster."""
        return obj.first_name
    first_name.short_description = 'Ad'

    def last_name(self, obj):
        """Soyad alanını göster."""
        return obj.last_name
    last_name.short_description = 'Soyad'

    def initials(self, obj):
        """Baş harfleri göster."""
        return obj.initials
    initials.short_description = 'Baş Harfler'

    class Meta:
        verbose_name = 'Takım Üyesi'
        verbose_name_plural = 'Takım Üyeleri'
