"""
Custom schema extensions for API documentation.
"""

from drf_spectacular.extensions import OpenApiViewExtension
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from drf_spectacular.types import OpenApiTypes
from rest_framework import status


class LanguageParameterExtension(OpenApiViewExtension):
    """
    Extension to add language parameter documentation to all ViewSets.
    """
    
    target_component = 'core.viewsets.LanguageAwareViewSet'
    
    def view_replacement(self):
        """Add language parameter to all endpoints."""
        return [
            OpenApiParameter(
                name='lang',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description='Language code for content (en for English, tr for Turkish)',
                examples=[
                    OpenApiExample('English', value='en'),
                    OpenApiExample('Turkish', value='tr'),
                ],
                enum=['en', 'tr'],
                default='en'
            ),
            OpenApiParameter(
                name='X-Language',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.HEADER,
                description='Alternative way to specify language via header',
                examples=[
                    OpenApiExample('English', value='en'),
                    OpenApiExample('Turkish', value='tr'),
                ],
                enum=['en', 'tr']
            )
        ]


# Common parameters for documentation
LANGUAGE_PARAMETER = OpenApiParameter(
    name='lang',
    type=OpenApiTypes.STR,
    location=OpenApiParameter.QUERY,
    description='Language code for content (en for English, tr for Turkish)',
    examples=[
        OpenApiExample('English', value='en'),
        OpenApiExample('Turkish', value='tr'),
    ],
    enum=['en', 'tr'],
    default='en'
)

LANGUAGE_HEADER = OpenApiParameter(
    name='X-Language',
    type=OpenApiTypes.STR,
    location=OpenApiParameter.HEADER,
    description='Language preference header',
    examples=[
        OpenApiExample('English', value='en'),
        OpenApiExample('Turkish', value='tr'),
    ],
    enum=['en', 'tr']
)

INCLUDE_INACTIVE_PARAMETER = OpenApiParameter(
    name='include_inactive',
    type=OpenApiTypes.BOOL,
    location=OpenApiParameter.QUERY,
    description='Include inactive items in results',
    default=False
)

SEARCH_PARAMETER = OpenApiParameter(
    name='search',
    type=OpenApiTypes.STR,
    location=OpenApiParameter.QUERY,
    description='Search term for filtering results'
)

FEATURED_PARAMETER = OpenApiParameter(
    name='featured',
    type=OpenApiTypes.BOOL,
    location=OpenApiParameter.QUERY,
    description='Filter by featured status'
)

CATEGORY_PARAMETER = OpenApiParameter(
    name='category',
    type=OpenApiTypes.STR,
    location=OpenApiParameter.QUERY,
    description='Filter by category'
)

# Common response examples
LANGUAGE_AWARE_RESPONSE_EXAMPLE = {
    'data': {
        'id': 1,
        'title': 'Example Title',
        'description': 'Example description',
        # ... other fields
    },
    '_meta': {
        'language': 'en',
        'database': 'default'
    }
}

MULTI_LANGUAGE_RESPONSE_EXAMPLE = {
    'data': {
        'en': [
            {
                'id': 1,
                'title': 'About Us',
                'description': 'Company information',
            }
        ],
        'tr': [
            {
                'id': 1,
                'title': 'Hakkımızda',
                'description': 'Şirket bilgileri',
            }
        ]
    },
    'meta': {
        'languages': ['en', 'tr'],
        'supported_languages': ['en', 'tr']
    }
}

ERROR_RESPONSE_EXAMPLE = {
    'error': 'Error message description',
    'detail': 'Detailed error information'
}

# Schema decorators for common endpoints
def language_aware_list_schema(summary, description, tags=None):
    """Schema decorator for language-aware list endpoints."""
    return extend_schema(
        summary=summary,
        description=description,
        tags=tags or [],
        parameters=[
            LANGUAGE_PARAMETER,
            LANGUAGE_HEADER,
            INCLUDE_INACTIVE_PARAMETER,
            SEARCH_PARAMETER,
            OpenApiParameter(
                name='page',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                description='Page number for pagination'
            ),
            OpenApiParameter(
                name='page_size',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                description='Number of items per page'
            )
        ],
        examples=[
            OpenApiExample(
                'Success Response',
                value={
                    'count': 10,
                    'next': 'http://example.com/api/v1/content/services/?page=2',
                    'previous': None,
                    'results': [LANGUAGE_AWARE_RESPONSE_EXAMPLE['data']]
                }
            )
        ]
    )


def language_aware_retrieve_schema(summary, description, tags=None):
    """Schema decorator for language-aware retrieve endpoints."""
    return extend_schema(
        summary=summary,
        description=description,
        tags=tags or [],
        parameters=[
            LANGUAGE_PARAMETER,
            LANGUAGE_HEADER
        ],
        examples=[
            OpenApiExample(
                'Success Response',
                value=LANGUAGE_AWARE_RESPONSE_EXAMPLE
            )
        ]
    )


def multi_language_schema(summary, description, tags=None):
    """Schema decorator for multi-language endpoints."""
    return extend_schema(
        summary=summary,
        description=description,
        tags=tags or [],
        parameters=[
            OpenApiParameter(
                name='languages',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description='Comma-separated list of language codes',
                examples=[
                    OpenApiExample('Both languages', value='en,tr'),
                    OpenApiExample('English only', value='en'),
                    OpenApiExample('Turkish only', value='tr'),
                ],
                default='en,tr'
            )
        ],
        examples=[
            OpenApiExample(
                'Multi-language Response',
                value=MULTI_LANGUAGE_RESPONSE_EXAMPLE
            )
        ]
    )


def error_responses():
    """Common error responses for documentation."""
    return {
        400: OpenApiExample(
            'Bad Request',
            value={'error': 'Invalid language parameter'}
        ),
        404: OpenApiExample(
            'Not Found',
            value={'error': 'Resource not found'}
        ),
        500: OpenApiExample(
            'Internal Server Error',
            value={'error': 'Internal server error occurred'}
        )
    }


# Custom schema for specific endpoints
ABOUT_US_SCHEMA = {
    'list': language_aware_list_schema(
        'List About Us entries',
        'Get all About Us entries in the specified language. '
        'Turkish content is automatically routed to the Turkish database.',
        tags=['Content']
    ),
    'retrieve': language_aware_retrieve_schema(
        'Get About Us entry',
        'Retrieve a specific About Us entry in the specified language.',
        tags=['Content']
    ),
    'featured': extend_schema(
        summary='Get featured About Us entry',
        description='Get the main About Us entry (usually only one).',
        tags=['Content'],
        parameters=[LANGUAGE_PARAMETER, LANGUAGE_HEADER]
    )
}

SERVICES_SCHEMA = {
    'list': language_aware_list_schema(
        'List services',
        'Get all services with filtering and search capabilities.',
        tags=['Content']
    ),
    'categories': extend_schema(
        summary='Get service categories',
        description='Get available service categories in the specified language.',
        tags=['Content'],
        parameters=[LANGUAGE_PARAMETER, LANGUAGE_HEADER]
    ),
    'featured': extend_schema(
        summary='Get featured services',
        description='Get services marked as featured.',
        tags=['Content'],
        parameters=[LANGUAGE_PARAMETER, LANGUAGE_HEADER]
    )
}

TEAM_SCHEMA = {
    'list': language_aware_list_schema(
        'List team members',
        'Get all team members with filtering capabilities.',
        tags=['Team']
    ),
    'featured': extend_schema(
        summary='Get featured team members',
        description='Get the first 3 team members by display order.',
        tags=['Team'],
        parameters=[LANGUAGE_PARAMETER, LANGUAGE_HEADER]
    ),
    'cards': extend_schema(
        summary='Get team member cards',
        description='Get team members in card format for display.',
        tags=['Team'],
        parameters=[LANGUAGE_PARAMETER, LANGUAGE_HEADER]
    )
}
