"""
Turkish admin interface for demo request models.

This admin configuration is specifically for Turkish demo request models and provides
a Turkish-language interface for managing Turkish demo requests.
"""

from django.contrib import admin
from django.utils.html import format_html
from .models_tr import DemoRequestTR


@admin.register(DemoRequestTR)
class DemoRequestTRAdmin(admin.ModelAdmin):
    """Turkish admin interface for DemoRequestTR model."""

    list_display = [
        'full_name', 'company_name', 'email', 'project_type',
        'budget_range', 'status', 'is_high_value_lead', 'urgency_level', 'created_at'
    ]

    list_filter = [
        'status', 'project_type', 'budget_range', 'company_size',
        'timeline', 'how_did_you_hear', 'created_at'
    ]

    search_fields = [
        'first_name', 'last_name', 'email', 'company_name',
        'project_description', 'notes'
    ]

    readonly_fields = [
        'created_at', 'updated_at', 'is_high_value_lead', 
        'urgency_level', 'contact_info', 'priority_score'
    ]

    fieldsets = (
        ('İletişim <PERSON>ilgiler<PERSON>', {
            'fields': ('first_name', 'last_name', 'email', 'phone')
        }),
        ('Şirket Bilgileri', {
            'fields': ('company_name', 'job_title', 'company_size', 'company_website')
        }),
        ('Proje Detayları', {
            'fields': ('project_type', 'budget_range', 'timeline',
                      'project_description', 'specific_requirements')
        }),
        ('Demo Planlama', {
            'fields': ('preferred_demo_date', 'preferred_demo_time')
        }),
        ('Müşteri Adayı Yönetimi', {
            'fields': ('status', 'notes')
        }),
        ('Pazarlama ve Analitik', {
            'fields': ('how_did_you_hear',)
        }),
        ('Hesaplanan Alanlar', {
            'fields': ('is_high_value_lead', 'urgency_level', 'contact_info', 'priority_score'),
            'classes': ('collapse',)
        }),
        ('Zaman Damgaları', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    date_hierarchy = 'created_at'
    ordering = ['-created_at']

    actions = [
        'mark_as_contacted', 'mark_as_demo_scheduled', 'mark_as_proposal_sent',
        'mark_as_high_priority', 'export_contact_info'
    ]

    def mark_as_contacted(self, request, queryset):
        """Seçili talepleri iletişim kuruldu olarak işaretle."""
        updated = queryset.update(status='contacted')
        self.message_user(request, f'{updated} demo talebi iletişim kuruldu olarak işaretlendi.')
    mark_as_contacted.short_description = "İletişim kuruldu olarak işaretle"

    def mark_as_demo_scheduled(self, request, queryset):
        """Seçili talepleri demo planlandı olarak işaretle."""
        updated = queryset.update(status='demo_scheduled')
        self.message_user(request, f'{updated} demo talebi demo planlandı olarak işaretlendi.')
    mark_as_demo_scheduled.short_description = "Demo planlandı olarak işaretle"

    def mark_as_proposal_sent(self, request, queryset):
        """Seçili talepleri teklif gönderildi olarak işaretle."""
        updated = queryset.update(status='proposal_sent')
        self.message_user(request, f'{updated} demo talebi teklif gönderildi olarak işaretlendi.')
    mark_as_proposal_sent.short_description = "Teklif gönderildi olarak işaretle"

    def mark_as_high_priority(self, request, queryset):
        """Seçili taleplere yüksek öncelik notu ekle."""
        for request_obj in queryset:
            if request_obj.notes:
                request_obj.notes += "\n[YÜKSEKÖNCELİK] Bu talep yüksek öncelikli olarak işaretlendi."
            else:
                request_obj.notes = "[YÜKSEKÖNCELİK] Bu talep yüksek öncelikli olarak işaretlendi."
            request_obj.save()
        self.message_user(request, f'{queryset.count()} demo talebi yüksek öncelikli olarak işaretlendi.')
    mark_as_high_priority.short_description = "Yüksek öncelikli olarak işaretle"

    def export_contact_info(self, request, queryset):
        """Seçili taleplerin iletişim bilgilerini dışa aktar."""
        # Bu fonksiyon gelecekte CSV export için genişletilebilir
        contact_count = queryset.count()
        self.message_user(request, f'{contact_count} kişinin iletişim bilgileri hazırlandı.')
    export_contact_info.short_description = "İletişim bilgilerini dışa aktar"

    def is_high_value_lead(self, obj):
        """Yüksek değerli müşteri adayı durumunu renkli olarak göster."""
        if obj.is_high_value_lead:
            return format_html('<span style="color: green; font-weight: bold;">✓ Yüksek Değer</span>')
        return format_html('<span style="color: orange;">Standart</span>')
    is_high_value_lead.short_description = 'Müşteri Adayı Kalitesi'

    def urgency_level(self, obj):
        """Aciliyet seviyesini renkli olarak göster."""
        urgency = obj.urgency_level
        if urgency == 'Yüksek':
            color = 'red'
        elif urgency == 'Orta':
            color = 'orange'
        else:
            color = 'green'
        return format_html('<span style="color: {}; font-weight: bold;">{}</span>', color, urgency)
    urgency_level.short_description = 'Aciliyet Seviyesi'

    def contact_info(self, obj):
        """İletişim bilgilerini formatlanmış olarak göster."""
        info = obj.contact_info
        formatted_info = []
        for key, value in info.items():
            if value:
                if key == 'name':
                    formatted_info.append(f"<strong>{value}</strong>")
                elif key == 'email':
                    formatted_info.append(f"📧 {value}")
                elif key == 'phone':
                    formatted_info.append(f"📞 {value}")
                elif key == 'company':
                    formatted_info.append(f"🏢 {value}")
                elif key == 'title':
                    formatted_info.append(f"💼 {value}")
        
        return format_html('<br>'.join(formatted_info))
    contact_info.short_description = 'İletişim Bilgileri'

    def priority_score(self, obj):
        """Öncelik puanını görsel olarak göster."""
        score = obj.get_priority_score()
        if score >= 20:
            color = 'red'
            level = 'Çok Yüksek'
        elif score >= 15:
            color = 'orange'
            level = 'Yüksek'
        elif score >= 10:
            color = 'blue'
            level = 'Orta'
        else:
            color = 'gray'
            level = 'Düşük'
        
        return format_html(
            '<span style="color: {}; font-weight: bold;">{} ({})</span>',
            color, score, level
        )
    priority_score.short_description = 'Öncelik Puanı'

    def get_status_color(self, obj):
        """Durum rengini al."""
        return obj.get_status_color()

    def full_name(self, obj):
        """Tam adı göster."""
        return obj.full_name
    full_name.short_description = 'Ad Soyad'

    class Meta:
        verbose_name = 'Demo Talebi'
        verbose_name_plural = 'Demo Talepleri'
