"""
API utilities for multi-language support and content negotiation.
"""

from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
from .routers import set_current_language, get_current_language, LanguageContext
from .models import ModelFactory


class ContentNegotiationMixin:
    """
    Mixin for API views to handle content negotiation and language switching.
    """
    
    def get_language_from_request(self, request):
        """
        Extract language from request with proper priority.
        
        Priority:
        1. URL parameter (?lang=tr)
        2. Custom header (X-Language)
        3. Accept-Language header
        4. Default language
        """
        
        # 1. URL parameter (highest priority for API)
        if hasattr(request, 'query_params'):
            language = request.query_params.get('lang')
        else:
            language = request.GET.get('lang')

        if language and self.is_valid_language(language):
            return language
        
        # 2. Custom language header
        language = request.META.get('HTTP_X_LANGUAGE')
        if language and self.is_valid_language(language):
            return language
        
        # 3. Accept-Language header
        accept_language = request.META.get('HTTP_ACCEPT_LANGUAGE', '')
        if accept_language:
            language = self.parse_accept_language_header(accept_language)
            if language:
                return language
        
        # 4. Default language
        return getattr(settings, 'DEFAULT_CONTENT_LANGUAGE', 'en')
    
    def is_valid_language(self, language):
        """Check if language is supported."""
        supported_languages = [lang[0] for lang in settings.LANGUAGES]
        return language in supported_languages
    
    def parse_accept_language_header(self, accept_language):
        """Parse Accept-Language header and return best match."""
        import re
        
        accept_language_re = re.compile(
            r'([a-z]{1,8}(?:-[a-z]{1,8})?)\s*(?:;\s*q\s*=\s*(1|0\.[0-9]+))?',
            re.IGNORECASE
        )
        
        languages = []
        for match in accept_language_re.finditer(accept_language):
            lang = match.group(1).lower()
            quality = match.group(2)
            
            if quality:
                try:
                    quality = float(quality)
                except ValueError:
                    quality = 1.0
            else:
                quality = 1.0
            
            languages.append((lang, quality))
        
        # Sort by quality (highest first)
        languages.sort(key=lambda x: x[1], reverse=True)
        
        # Find the best matching supported language
        supported_languages = [lang[0] for lang in settings.LANGUAGES]
        
        for lang, quality in languages:
            # Check exact match first
            if lang in supported_languages:
                return lang
            
            # Check language prefix (e.g., 'en-US' -> 'en')
            lang_prefix = lang.split('-')[0]
            if lang_prefix in supported_languages:
                return lang_prefix
        
        return None
    
    def setup_language_context(self, request):
        """Set up language context for the request."""
        language = self.get_language_from_request(request)
        
        # Set language context
        set_current_language(language)
        
        # Store in request for serializers
        request.api_language = language
        
        return language
    
    def get_serializer_context(self):
        """Add language context to serializer."""
        context = super().get_serializer_context() if hasattr(super(), 'get_serializer_context') else {}
        
        if hasattr(self.request, 'api_language'):
            context['language'] = self.request.api_language
        else:
            context['language'] = get_current_language()
        
        return context


class MultiLanguageResponseMixin:
    """
    Mixin to provide responses in multiple languages.
    """
    
    def get_multi_language_response(self, data, languages=None):
        """
        Return data in multiple languages.
        
        Args:
            data: The data to serialize
            languages: List of languages to include (default: ['en', 'tr'])
        
        Returns:
            Response with data in multiple languages
        """
        if languages is None:
            languages = ['en', 'tr']
        
        multi_lang_data = {}
        
        for language in languages:
            with LanguageContext(language):
                try:
                    # Get serializer for this language
                    serializer = self.get_serializer(data, context={'language': language})
                    multi_lang_data[language] = serializer.data
                except Exception as e:
                    multi_lang_data[language] = {'error': str(e)}
        
        return Response(multi_lang_data)


class LanguageHeaderMixin:
    """
    Mixin to add language information to response headers.
    """
    
    def finalize_response(self, request, response, *args, **kwargs):
        """Add language headers to response."""
        response = super().finalize_response(request, response, *args, **kwargs)
        
        # Add language information to headers
        current_language = get_current_language()
        response['Content-Language'] = current_language
        response['X-Database'] = 'turkish' if current_language == 'tr' else 'default'
        response['X-Supported-Languages'] = ','.join([lang[0] for lang in settings.LANGUAGES])
        
        return response


def create_language_aware_response(data, language=None, status_code=status.HTTP_200_OK):
    """
    Create a response with language metadata.
    
    Args:
        data: Response data
        language: Language code (defaults to current language)
        status_code: HTTP status code
    
    Returns:
        Response with language metadata
    """
    if language is None:
        language = get_current_language()
    
    response_data = {
        'data': data,
        'meta': {
            'language': language,
            'database': 'turkish' if language == 'tr' else 'default',
            'supported_languages': [lang[0] for lang in settings.LANGUAGES]
        }
    }
    
    response = Response(response_data, status=status_code)
    response['Content-Language'] = language
    response['X-Database'] = 'turkish' if language == 'tr' else 'default'
    
    return response


def get_model_for_request(model_name, request):
    """
    Get the appropriate model class based on request language.
    
    Args:
        model_name: Base model name (e.g., 'AboutUs')
        request: HTTP request object
    
    Returns:
        Model class for the request language
    """
    # Try to get language from request
    language = 'en'  # default
    
    if hasattr(request, 'api_language'):
        language = request.api_language
    elif hasattr(request, 'query_params') and 'lang' in request.query_params:
        language = request.query_params['lang']
    elif 'HTTP_X_LANGUAGE' in request.META:
        language = request.META['HTTP_X_LANGUAGE']
    
    return ModelFactory.get_model(model_name, language)


def validate_language_parameter(language):
    """
    Validate language parameter.
    
    Args:
        language: Language code to validate
    
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not language:
        return False, "Language parameter is required"
    
    supported_languages = [lang[0] for lang in settings.LANGUAGES]
    if language not in supported_languages:
        return False, f"Language '{language}' is not supported. Supported languages: {', '.join(supported_languages)}"
    
    return True, None


class APILanguageMiddleware:
    """
    Middleware specifically for API requests to handle language negotiation.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Only process API requests
        if request.path.startswith('/api/'):
            self.process_api_request(request)
        
        response = self.get_response(request)
        
        # Add language headers to API responses
        if request.path.startswith('/api/'):
            self.process_api_response(request, response)
        
        return response
    
    def process_api_request(self, request):
        """Process API request for language negotiation."""
        from core.middleware import APILanguageNegotiationMixin
        
        mixin = APILanguageNegotiationMixin()
        language = mixin.get_language_from_request(request)
        mixin.set_language_context(request, language)
    
    def process_api_response(self, request, response):
        """Add language headers to API response."""
        current_language = get_current_language()
        response['Content-Language'] = current_language
        response['X-Database'] = 'turkish' if current_language == 'tr' else 'default'
        response['X-Supported-Languages'] = ','.join([lang[0] for lang in settings.LANGUAGES])


def get_available_languages():
    """
    Get list of available languages with metadata.
    
    Returns:
        List of language dictionaries with code, name, and database info
    """
    languages = []
    
    for code, name in settings.LANGUAGES:
        languages.append({
            'code': code,
            'name': name,
            'database': 'turkish' if code == 'tr' else 'default',
            'is_default': code == getattr(settings, 'DEFAULT_CONTENT_LANGUAGE', 'en')
        })
    
    return languages
