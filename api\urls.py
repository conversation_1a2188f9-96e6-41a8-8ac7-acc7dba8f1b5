"""
Main API URL configuration for Bean Software multi-language API.

This file organizes all API endpoints with proper versioning and language support.
"""

from django.urls import path, include
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView, SpectacularRedocView
from core.api_utils import get_available_languages
from core.routers import get_current_language


@api_view(['GET'])
def api_root(request):
    """
    API root endpoint providing information about available endpoints and languages.
    """
    return Response({
        'message': 'Welcome to Bean Software Multi-Language API',
        'version': '1.0.0',
        'current_language': get_current_language(),
        'supported_languages': get_available_languages(),
        'endpoints': {
            'content': {
                'about_us': '/api/v1/content/about-us/',
                'services': '/api/v1/content/services/',
                'additional_services': '/api/v1/content/additional-services/',
                'featured_resources': '/api/v1/content/featured-resources/',
                'social_media_links': '/api/v1/content/social-media-links/',
                'summary': '/api/v1/content/summary/',
            },
            'team': {
                'members': '/api/v1/team/members/',
                'stats': '/api/v1/team/stats/',
            },
            'core': {
                'database_info': '/api/v1/core/database-info/',
                'languages': '/api/v1/languages/',
            },
            'docs': {
                'schema': '/api/v1/schema/',
                'swagger': '/api/v1/docs/',
                'redoc': '/api/v1/redoc/',
            }
        },
        'usage': {
            'language_parameter': 'Add ?lang=tr for Turkish content, ?lang=en for English',
            'language_header': 'Use X-Language header to specify language',
            'multi_language': 'Use /multi_language/ endpoint for content in all languages',
            'pagination': 'Use ?page=N for pagination',
            'filtering': 'Use query parameters for filtering (e.g., ?category=web_development)',
        }
    })


@api_view(['GET'])
def language_info(request):
    """
    Endpoint providing detailed language and database information.
    """
    current_language = get_current_language()
    
    return Response({
        'current_language': current_language,
        'current_database': 'turkish' if current_language == 'tr' else 'default',
        'available_languages': get_available_languages(),
        'language_switching': {
            'url_parameter': '?lang=tr or ?lang=en',
            'header': 'X-Language: tr or X-Language: en',
            'cookie': 'bean_language=tr or bean_language=en',
        },
        'database_routing': {
            'english_content': 'default database (SQLite: db.sqlite3)',
            'turkish_content': 'turkish database (SQLite: db_turkish.sqlite3)',
            'automatic_routing': 'Content is automatically routed based on language'
        }
    })


app_name = 'api'

# API v1 URL patterns
v1_patterns = [
    # Core API endpoints
    path('core/', include('core.api_urls')),
    
    # Content API endpoints
    path('content/', include('content.api_urls')),
    
    # Team API endpoints
    path('team/', include('team.api_urls')),
    
    # Language information
    path('languages/', language_info, name='language-info'),

    # API documentation
    path('schema/', SpectacularAPIView.as_view(), name='schema'),
    path('docs/', SpectacularSwaggerView.as_view(url_name='api:v1:schema'), name='swagger-ui'),
    path('redoc/', SpectacularRedocView.as_view(url_name='api:v1:schema'), name='redoc'),
]

urlpatterns = [
    # API root
    path('', api_root, name='api-root'),
    
    # Version 1 API
    path('v1/', include((v1_patterns, 'v1'), namespace='v1')),
    
    # Default to v1 for now
    path('', include((v1_patterns, 'v1'), namespace='default')),
]
