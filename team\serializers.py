"""
Serializers for team models with multi-language support.
"""

from rest_framework import serializers
from core.serializers import (
    LanguageAwareSerializer, TimestampedSerializer, 
    ActiveModelSerializer, DisplayOrderSerializer
)
from .models import TeamMember


class TeamMemberSerializer(LanguageAwareSerializer, TimestampedSerializer,
                          ActiveModelSerializer, DisplayOrderSerializer):
    """
    Serializer for TeamMember model with language awareness.
    """
    
    base_model_name = 'TeamMember'
    
    # Computed fields
    first_name = serializers.SerializerMethodField()
    last_name = serializers.SerializerMethodField()
    initials = serializers.SerializerMethodField()
    has_social_links = serializers.SerializerMethodField()
    social_links = serializers.SerializerMethodField()
    contact_methods = serializers.SerializerMethodField()
    short_bio = serializers.SerializerMethodField()
    expertise_areas = serializers.SerializerMethodField()
    
    class Meta:
        model = TeamMember  # This will be overridden by LanguageAwareSerializer
        fields = [
            'id', 'full_name', 'first_name', 'last_name', 'position',
            'bio', 'short_bio', 'profile_image', 'email', 'linkedin_url',
            'github_url', 'is_active', 'display_order', 'initials',
            'has_social_links', 'social_links', 'contact_methods',
            'expertise_areas', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'first_name', 'last_name',
            'initials', 'has_social_links', 'social_links', 'contact_methods',
            'short_bio', 'expertise_areas'
        ]
    
    def get_first_name(self, obj):
        """Get first name from full name."""
        if hasattr(obj, 'first_name'):
            return obj.first_name
        return obj.full_name.split()[0] if obj.full_name else ""
    
    def get_last_name(self, obj):
        """Get last name from full name."""
        if hasattr(obj, 'last_name'):
            return obj.last_name
        name_parts = obj.full_name.split()
        return " ".join(name_parts[1:]) if len(name_parts) > 1 else ""
    
    def get_initials(self, obj):
        """Get initials from full name."""
        if hasattr(obj, 'initials'):
            return obj.initials
        name_parts = obj.full_name.split()
        if len(name_parts) >= 2:
            return f"{name_parts[0][0]}{name_parts[-1][0]}".upper()
        elif len(name_parts) == 1:
            return name_parts[0][:2].upper()
        return "TM"
    
    def get_has_social_links(self, obj):
        """Check if team member has social links."""
        if hasattr(obj, 'has_social_links'):
            return obj.has_social_links
        return bool(obj.linkedin_url or obj.github_url)
    
    def get_social_links(self, obj):
        """Get social media links."""
        if hasattr(obj, 'get_social_links'):
            return obj.get_social_links()
        
        links = {}
        if obj.linkedin_url:
            links['linkedin'] = obj.linkedin_url
        if obj.github_url:
            links['github'] = obj.github_url
        return links
    
    def get_contact_methods(self, obj):
        """Get available contact methods."""
        if hasattr(obj, 'contact_methods'):
            return obj.contact_methods
        
        methods = []
        if obj.email:
            methods.append({
                'type': 'email',
                'value': obj.email,
                'display': 'Email',
                'icon': 'fas fa-envelope'
            })
        if obj.linkedin_url:
            methods.append({
                'type': 'linkedin',
                'value': obj.linkedin_url,
                'display': 'LinkedIn',
                'icon': 'fab fa-linkedin'
            })
        if obj.github_url:
            methods.append({
                'type': 'github',
                'value': obj.github_url,
                'display': 'GitHub',
                'icon': 'fab fa-github'
            })
        return methods
    
    def get_short_bio(self, obj):
        """Get shortened bio."""
        if hasattr(obj, 'short_bio'):
            return obj.short_bio
        
        if not obj.bio:
            return ""
        
        # Return first 150 characters with ellipsis if longer
        if len(obj.bio) <= 150:
            return obj.bio
        
        # Find the last space before 150 characters to avoid cutting words
        truncated = obj.bio[:150]
        last_space = truncated.rfind(' ')
        if last_space > 100:  # Only truncate at word boundary if it's not too short
            truncated = truncated[:last_space]
        
        return f"{truncated}..."
    
    def get_expertise_areas(self, obj):
        """Get expertise areas from bio or position."""
        if hasattr(obj, 'get_expertise_areas'):
            return obj.get_expertise_areas()
        
        # Simple keyword extraction
        expertise_keywords = [
            'Python', 'Django', 'JavaScript', 'React', 'Vue.js', 'Angular',
            'Node.js', 'PHP', 'Laravel', 'Java', 'Spring', 'C#', '.NET',
            'Mobile', 'iOS', 'Android', 'Flutter', 'React Native',
            'UI/UX', 'Design', 'Frontend', 'Backend', 'Full-stack',
            'DevOps', 'AWS', 'Azure', 'Docker', 'Kubernetes',
            'Database', 'PostgreSQL', 'MySQL', 'MongoDB',
            'Machine Learning', 'AI', 'Data Science'
        ]
        
        found_expertise = []
        text_to_search = f"{obj.position} {obj.bio or ''}".lower()
        
        for keyword in expertise_keywords:
            if keyword.lower() in text_to_search:
                found_expertise.append(keyword)
        
        return found_expertise[:5]  # Return max 5 expertise areas
    
    def validate_email(self, value):
        """Validate email format."""
        if value:
            # Additional email validation can be added here
            pass
        return value
    
    def validate(self, data):
        """Custom validation for the entire object."""
        # Ensure at least one contact method is provided
        email = data.get('email')
        linkedin_url = data.get('linkedin_url')
        github_url = data.get('github_url')
        
        if not any([email, linkedin_url, github_url]):
            raise serializers.ValidationError(
                "At least one contact method (email, LinkedIn, or GitHub) must be provided."
            )
        
        return data


class TeamMemberListSerializer(TeamMemberSerializer):
    """Simplified serializer for TeamMember list view."""
    
    class Meta(TeamMemberSerializer.Meta):
        fields = [
            'id', 'full_name', 'first_name', 'last_name', 'position',
            'profile_image', 'initials', 'has_social_links', 'is_active',
            'display_order'
        ]


class TeamMemberCardSerializer(TeamMemberSerializer):
    """Card-style serializer for TeamMember display."""
    
    class Meta(TeamMemberSerializer.Meta):
        fields = [
            'id', 'full_name', 'position', 'short_bio', 'profile_image',
            'initials', 'social_links', 'expertise_areas', 'is_active'
        ]


class TeamSummarySerializer(serializers.Serializer):
    """
    Serializer for team summary/statistics.
    """
    
    total_members = serializers.IntegerField()
    active_members = serializers.IntegerField()
    positions = serializers.ListField(child=serializers.CharField())
    expertise_areas = serializers.ListField(child=serializers.CharField())
    language = serializers.CharField()
    database = serializers.CharField()
    
    def to_representation(self, instance):
        """Calculate team statistics."""
        from core.routers import get_current_language
        from core.models import ModelFactory
        
        language = get_current_language()
        
        try:
            team_model = ModelFactory.get_model('TeamMember', language)
            all_members = team_model.objects.all()
            active_members = all_members.filter(is_active=True)
            
            # Get unique positions
            positions = list(set(member.position for member in active_members if member.position))
            
            # Get all expertise areas
            all_expertise = []
            for member in active_members:
                if hasattr(member, 'get_expertise_areas'):
                    all_expertise.extend(member.get_expertise_areas())
            
            # Get unique expertise areas
            expertise_areas = list(set(all_expertise))
            
            return {
                'total_members': all_members.count(),
                'active_members': active_members.count(),
                'positions': positions,
                'expertise_areas': expertise_areas,
                'language': language,
                'database': 'turkish' if language == 'tr' else 'default'
            }
        except Exception as e:
            return {
                'error': str(e),
                'language': language,
                'database': 'turkish' if language == 'tr' else 'default'
            }
