"""
Base serializers for multi-language support.

This module contains base serializer classes and utilities for handling
both English and Turkish models in the API.
"""

from rest_framework import serializers
from .routers import get_current_language
from .models import ModelFactory


class LanguageAwareSerializer(serializers.ModelSerializer):
    """
    Base serializer that automatically works with language-specific models.
    """
    
    # Override this in subclasses to specify the base model name
    base_model_name = None
    
    def __init__(self, *args, **kwargs):
        # Get language from context or use current language
        self.language = self.get_language_from_context(kwargs)
        
        # Get the appropriate model class for the language
        if self.base_model_name:
            try:
                self.Meta.model = ModelFactory.get_model(self.base_model_name, self.language)
            except ValueError:
                # Fall back to the original model if Turkish version doesn't exist
                pass
        
        super().__init__(*args, **kwargs)
    
    def get_language_from_context(self, kwargs):
        """Extract language from serializer context."""
        context = kwargs.get('context', {})
        
        # Try to get language from context
        if 'language' in context:
            return context['language']
        
        # Try to get language from request
        request = context.get('request')
        if request:
            # Check for language parameter
            if hasattr(request, 'query_params') and 'lang' in request.query_params:
                return request.query_params['lang']
            
            # Check for custom language header
            if hasattr(request, 'META') and 'HTTP_X_LANGUAGE' in request.META:
                return request.META['HTTP_X_LANGUAGE']
            
            # Check for detected language from middleware
            if hasattr(request, 'detected_language'):
                return request.detected_language
        
        # Fall back to current language context
        return get_current_language()
    
    def to_representation(self, instance):
        """Add language information to the serialized data."""
        data = super().to_representation(instance)
        
        # Add language metadata
        data['_meta'] = {
            'language': self.language,
            'database': 'turkish' if self.language == 'tr' else 'default'
        }
        
        return data


class MultiLanguageSerializer(serializers.Serializer):
    """
    Serializer that can return data in multiple languages.
    """
    
    base_model_name = None
    base_serializer_class = None
    
    def __init__(self, *args, **kwargs):
        self.languages = kwargs.pop('languages', ['en', 'tr'])
        super().__init__(*args, **kwargs)
    
    def to_representation(self, instance):
        """Return data in all requested languages."""
        data = {}
        
        for language in self.languages:
            try:
                # Create serializer for this language
                serializer_class = self.base_serializer_class or LanguageAwareSerializer
                context = self.context.copy() if self.context else {}
                context['language'] = language
                
                serializer = serializer_class(instance, context=context)
                data[language] = serializer.data
                
            except Exception as e:
                data[language] = {'error': str(e)}
        
        return data


class LanguageFieldMixin:
    """
    Mixin to add language-specific field handling.
    """
    
    def get_localized_field(self, obj, field_name, language=None):
        """
        Get a localized version of a field value.
        
        This can be extended to support field-level translations.
        """
        if language is None:
            language = get_current_language()
        
        # For now, just return the field value
        # This can be extended to support translation dictionaries
        return getattr(obj, field_name, None)


class TimestampedSerializer(serializers.ModelSerializer):
    """
    Base serializer for models with timestamp fields.
    """
    
    created_at = serializers.DateTimeField(read_only=True, format='%Y-%m-%d %H:%M:%S')
    updated_at = serializers.DateTimeField(read_only=True, format='%Y-%m-%d %H:%M:%S')


class ActiveModelSerializer(serializers.ModelSerializer):
    """
    Base serializer for models with is_active field.
    """
    
    def get_queryset(self):
        """Return only active objects by default."""
        queryset = super().get_queryset() if hasattr(super(), 'get_queryset') else None
        if queryset is not None and hasattr(queryset.model, 'is_active'):
            return queryset.filter(is_active=True)
        return queryset


class DisplayOrderSerializer(serializers.ModelSerializer):
    """
    Base serializer for models with display_order field.
    """
    
    class Meta:
        fields = '__all__'
    
    def get_queryset(self):
        """Return objects ordered by display_order."""
        queryset = super().get_queryset() if hasattr(super(), 'get_queryset') else None
        if queryset is not None and hasattr(queryset.model, 'display_order'):
            return queryset.order_by('display_order')
        return queryset


def create_language_aware_serializer(base_serializer_class, base_model_name):
    """
    Factory function to create a language-aware version of any serializer.
    
    Args:
        base_serializer_class: The base serializer class
        base_model_name: Name of the base model (e.g., 'AboutUs')
    
    Returns:
        Language-aware serializer class
    """
    
    class DynamicLanguageAwareSerializer(LanguageAwareSerializer, base_serializer_class):
        base_model_name = base_model_name
        
        class Meta(base_serializer_class.Meta):
            pass
    
    return DynamicLanguageAwareSerializer


def get_serializer_for_language(serializer_class, language):
    """
    Get a serializer instance configured for a specific language.
    
    Args:
        serializer_class: The serializer class
        language: Language code ('en' or 'tr')
    
    Returns:
        Serializer instance configured for the language
    """
    context = {'language': language}
    return serializer_class(context=context)


class LanguageChoiceField(serializers.ChoiceField):
    """
    Custom field for language selection.
    """
    
    def __init__(self, **kwargs):
        choices = [
            ('en', 'English'),
            ('tr', 'Türkçe'),
        ]
        super().__init__(choices=choices, **kwargs)


class DatabaseInfoField(serializers.Field):
    """
    Custom field that returns database information based on language.
    """
    
    def to_representation(self, value):
        language = get_current_language()
        return {
            'language': language,
            'database': 'turkish' if language == 'tr' else 'default',
            'is_turkish': language == 'tr'
        }
    
    def to_internal_value(self, data):
        # This field is read-only
        raise serializers.ValidationError("This field is read-only.")
