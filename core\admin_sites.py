"""
Language-specific admin sites for multi-language content management.

This module creates separate admin sites for English and Turkish content,
providing localized interfaces for each language.
"""

from django.contrib import admin
from django.contrib.auth.models import User, Group
from django.contrib.auth.admin import UserAdmin, GroupAdmin
from django.utils.translation import gettext_lazy as _


class TurkishAdminSite(admin.AdminSite):
    """
    Turkish admin site for managing Turkish content.
    
    This admin site is specifically designed for Turkish content management
    and provides a Turkish-language interface.
    """
    
    site_header = 'Bean Software Türkçe Yönetim Paneli'
    site_title = 'Bean Software TR Admin'
    index_title = 'Türkçe İçerik Yönetimi'
    
    def __init__(self, name='turkish_admin'):
        super().__init__(name)
    
    def has_permission(self, request):
        """
        Check if the user has permission to access the Turkish admin.
        """
        return request.user.is_active and request.user.is_staff
    
    def index(self, request, extra_context=None):
        """
        Display the main admin index page with Turkish context.
        """
        extra_context = extra_context or {}
        extra_context.update({
            'title': 'Türkçe İçerik Yönetimi',
            'subtitle': 'Türkçe veritabanındaki içerikleri yönetin',
            'database_info': {
                'name': 'Turkish Database',
                'description': 'Türk yasalarına uygun olarak Türkiye\'de saklanan içerikler'
            }
        })
        return super().index(request, extra_context)


class EnglishAdminSite(admin.AdminSite):
    """
    English admin site for managing English content.
    
    This is the default admin site enhanced with English-specific branding.
    """
    
    site_header = 'Bean Software English Management Panel'
    site_title = 'Bean Software EN Admin'
    index_title = 'English Content Management'
    
    def __init__(self, name='english_admin'):
        super().__init__(name)
    
    def has_permission(self, request):
        """
        Check if the user has permission to access the English admin.
        """
        return request.user.is_active and request.user.is_staff
    
    def index(self, request, extra_context=None):
        """
        Display the main admin index page with English context.
        """
        extra_context = extra_context or {}
        extra_context.update({
            'title': 'English Content Management',
            'subtitle': 'Manage English content in the default database',
            'database_info': {
                'name': 'Default Database',
                'description': 'English and international content storage'
            }
        })
        return super().index(request, extra_context)


# Create admin site instances
turkish_admin_site = TurkishAdminSite(name='turkish_admin')
english_admin_site = EnglishAdminSite(name='english_admin')

# Register User and Group models in both admin sites
turkish_admin_site.register(User, UserAdmin)
turkish_admin_site.register(Group, GroupAdmin)
english_admin_site.register(User, UserAdmin)
english_admin_site.register(Group, GroupAdmin)


def register_turkish_models():
    """
    Register all Turkish models with the Turkish admin site.
    """
    # Content models
    try:
        from content.models_tr import AboutUsTR, ServiceTR, AdditionalServiceTR, FeaturedResourceTR, SocialMediaLinkTR
        from content.admin_tr import (
            AboutUsTRAdmin, ServiceTRAdmin, AdditionalServiceTRAdmin,
            FeaturedResourceTRAdmin, SocialMediaLinkTRAdmin
        )
        
        turkish_admin_site.register(AboutUsTR, AboutUsTRAdmin)
        turkish_admin_site.register(ServiceTR, ServiceTRAdmin)
        turkish_admin_site.register(AdditionalServiceTR, AdditionalServiceTRAdmin)
        turkish_admin_site.register(FeaturedResourceTR, FeaturedResourceTRAdmin)
        turkish_admin_site.register(SocialMediaLinkTR, SocialMediaLinkTRAdmin)
    except ImportError:
        pass
    
    # Team models
    try:
        from team.models_tr import TeamMemberTR
        from team.admin_tr import TeamMemberTRAdmin
        
        turkish_admin_site.register(TeamMemberTR, TeamMemberTRAdmin)
    except ImportError:
        pass
    
    # Technology models
    try:
        from technologies.models_tr import TechnologyCategoryTR, TechnologyTR
        from technologies.admin_tr import TechnologyCategoryTRAdmin, TechnologyTRAdmin
        
        turkish_admin_site.register(TechnologyCategoryTR, TechnologyCategoryTRAdmin)
        turkish_admin_site.register(TechnologyTR, TechnologyTRAdmin)
    except ImportError:
        pass
    
    # Demo models
    try:
        from demos.models_tr import DemoRequestTR
        from demos.admin_tr import DemoRequestTRAdmin
        
        turkish_admin_site.register(DemoRequestTR, DemoRequestTRAdmin)
    except ImportError:
        pass


def register_english_models():
    """
    Register all English models with the English admin site.
    """
    # Content models
    try:
        from content.models import AboutUs, Service, AdditionalService, FeaturedResource, SocialMediaLink
        from content.admin import (
            AboutUsAdmin, ServiceAdmin, AdditionalServiceAdmin,
            FeaturedResourceAdmin, SocialMediaLinkAdmin
        )
        
        english_admin_site.register(AboutUs, AboutUsAdmin)
        english_admin_site.register(Service, ServiceAdmin)
        english_admin_site.register(AdditionalService, AdditionalServiceAdmin)
        english_admin_site.register(FeaturedResource, FeaturedResourceAdmin)
        english_admin_site.register(SocialMediaLink, SocialMediaLinkAdmin)
    except ImportError:
        pass
    
    # Team models
    try:
        from team.models import TeamMember
        from team.admin import TeamMemberAdmin
        
        english_admin_site.register(TeamMember, TeamMemberAdmin)
    except ImportError:
        pass
    
    # Technology models
    try:
        from technologies.models import TechnologyCategory, Technology
        from technologies.admin import TechnologyCategoryAdmin, TechnologyAdmin
        
        english_admin_site.register(TechnologyCategory, TechnologyCategoryAdmin)
        english_admin_site.register(Technology, TechnologyAdmin)
    except ImportError:
        pass
    
    # Demo models
    try:
        from demos.models import DemoRequest
        from demos.admin import DemoRequestAdmin
        
        english_admin_site.register(DemoRequest, DemoRequestAdmin)
    except ImportError:
        pass


# Register models when this module is imported
register_turkish_models()
register_english_models()


class AdminSiteManager:
    """
    Manager class for handling multiple admin sites.
    """
    
    @staticmethod
    def get_admin_site_for_language(language):
        """
        Get the appropriate admin site for a given language.
        
        Args:
            language: Language code ('en' or 'tr')
        
        Returns:
            Admin site instance
        """
        if language == 'tr':
            return turkish_admin_site
        return english_admin_site
    
    @staticmethod
    def get_admin_url_for_language(language, url_name, *args, **kwargs):
        """
        Get admin URL for a specific language.
        
        Args:
            language: Language code ('en' or 'tr')
            url_name: URL name
            *args, **kwargs: URL arguments
        
        Returns:
            Admin URL for the specified language
        """
        admin_site = AdminSiteManager.get_admin_site_for_language(language)
        return admin_site.reverse(url_name, args=args, kwargs=kwargs)
    
    @staticmethod
    def get_available_admin_sites():
        """
        Get information about all available admin sites.
        
        Returns:
            Dictionary with admin site information
        """
        return {
            'turkish': {
                'site': turkish_admin_site,
                'name': 'Turkish Admin',
                'url': '/admin/tr/',
                'language': 'tr',
                'database': 'turkish'
            },
            'english': {
                'site': english_admin_site,
                'name': 'English Admin',
                'url': '/admin/en/',
                'language': 'en',
                'database': 'default'
            },
            'default': {
                'site': admin.site,
                'name': 'Default Admin',
                'url': '/admin/',
                'language': 'en',
                'database': 'default'
            }
        }
