"""
API URL routing for team models with multi-language support.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .viewsets import TeamMemberViewSet, TeamStatsViewSet

# Create router for team API endpoints
router = DefaultRouter()
router.register(r'members', TeamMemberViewSet, basename='team-members')
router.register(r'stats', TeamStatsViewSet, basename='team-stats')

app_name = 'team_api'

urlpatterns = [
    path('', include(router.urls)),
]
