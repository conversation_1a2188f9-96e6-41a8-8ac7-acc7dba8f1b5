"""
API URL routing for core multi-language functionality.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .viewsets import DatabaseInfoViewSet

# Create router for core API endpoints
router = DefaultRouter()
router.register(r'database-info', DatabaseInfoViewSet, basename='database-info')

app_name = 'core_api'

urlpatterns = [
    path('', include(router.urls)),
]
